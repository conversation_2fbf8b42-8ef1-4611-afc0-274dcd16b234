#!/usr/bin/env python
"""
最终测试注册功能
"""
import requests
import json
import time

def test_single_registration():
    """测试单次注册"""
    print("🧪 测试单次注册...")
    
    api_url = "http://127.0.0.1:8001/api/register/"
    test_email = f"single_test_{int(time.time())}@example.com"
    
    user_data = {
        "email": test_email,
        "password": "testpass123",
        "password_confirm": "testpass123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    headers = {'Content-Type': 'application/json'}
    
    try:
        response = requests.post(api_url, json=user_data, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 201:
            response_data = response.json()
            if response_data.get('success'):
                print("✅ 单次注册成功")
                return True
            else:
                print("❌ 响应状态码201但success=False")
                return False
        else:
            print("❌ 单次注册失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_duplicate_registration():
    """测试重复邮箱注册"""
    print("\n🧪 测试重复邮箱注册...")
    
    api_url = "http://127.0.0.1:8001/api/register/"
    test_email = f"duplicate_test_{int(time.time())}@example.com"
    
    user_data = {
        "email": test_email,
        "password": "testpass123",
        "password_confirm": "testpass123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    headers = {'Content-Type': 'application/json'}
    
    try:
        # 第一次注册
        print("📝 第一次注册...")
        response1 = requests.post(api_url, json=user_data, headers=headers)
        print(f"第一次 - 状态码: {response1.status_code}")
        print(f"第一次 - 响应: {response1.json()}")
        
        if response1.status_code != 201:
            print("❌ 第一次注册失败")
            return False
        
        # 等待一秒
        time.sleep(1)
        
        # 第二次注册（相同邮箱）
        print("\n📝 第二次注册（相同邮箱）...")
        response2 = requests.post(api_url, json=user_data, headers=headers)
        print(f"第二次 - 状态码: {response2.status_code}")
        print(f"第二次 - 响应: {response2.json()}")
        
        if response2.status_code == 400:
            response_data = response2.json()
            if not response_data.get('success', True):
                print("✅ 第二次注册正确被拒绝")
                return True
            else:
                print("❌ 第二次注册状态码400但success=True")
                return False
        else:
            print("❌ 第二次注册应该返回400但没有")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_rapid_requests():
    """测试快速连续请求"""
    print("\n🧪 测试快速连续请求...")
    
    api_url = "http://127.0.0.1:8001/api/register/"
    test_email = f"rapid_test_{int(time.time())}@example.com"
    
    user_data = {
        "email": test_email,
        "password": "testpass123",
        "password_confirm": "testpass123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    headers = {'Content-Type': 'application/json'}
    
    try:
        # 快速发送两个请求
        print("📝 快速发送两个相同的注册请求...")
        
        import threading
        results = []
        
        def make_request(request_id):
            try:
                response = requests.post(api_url, json=user_data, headers=headers)
                results.append({
                    'id': request_id,
                    'status_code': response.status_code,
                    'response': response.json()
                })
            except Exception as e:
                results.append({
                    'id': request_id,
                    'error': str(e)
                })
        
        # 创建两个线程同时发送请求
        thread1 = threading.Thread(target=make_request, args=(1,))
        thread2 = threading.Thread(target=make_request, args=(2,))
        
        thread1.start()
        thread2.start()
        
        thread1.join()
        thread2.join()
        
        # 分析结果
        success_count = 0
        for result in results:
            request_id = result['id']
            if 'error' in result:
                print(f"请求{request_id}: ❌ 错误 - {result['error']}")
            else:
                status_code = result['status_code']
                response_data = result['response']
                print(f"请求{request_id}: 状态码{status_code} - {response_data}")
                
                if status_code == 201 and response_data.get('success'):
                    success_count += 1
        
        if success_count == 1:
            print("✅ 快速连续请求正确处理：只有一个成功")
            return True
        else:
            print(f"❌ 快速连续请求处理异常：{success_count}个成功")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始最终注册功能测试...")
    print("=" * 50)
    
    tests = [
        ("单次注册", test_single_registration),
        ("重复邮箱注册", test_duplicate_registration),
        ("快速连续请求", test_rapid_requests),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！注册功能完全正常！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
