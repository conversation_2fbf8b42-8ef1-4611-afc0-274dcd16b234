# 📧 SendGrid 完整使用指南

## 🌟 SendGrid 简介

SendGrid 是全球领先的邮件发送服务，特别适合开发者和企业使用：

### ✅ **优势**
- **免费额度**：每天100封邮件永久免费
- **高送达率**：99%+ 的邮件送达率
- **简单集成**：支持SMTP和API两种方式
- **详细统计**：邮件发送、打开、点击等数据
- **全球CDN**：快速稳定的邮件发送
- **反垃圾邮件**：自动处理退信和投诉

### 📊 **价格**
- **免费版**：100封/天，永久免费
- **基础版**：$14.95/月，40,000封/月
- **高级版**：$89.95/月，100,000封/月

## 🚀 快速开始

### 步骤1：注册SendGrid账户

1. 访问 [SendGrid官网](https://sendgrid.com/)
2. 点击 "Start for free" 注册免费账户
3. 填写基本信息：
   - 邮箱地址
   - 密码
   - 公司名称（可以填个人）
   - 网站URL（可以填GitHub或个人网站）

### 步骤2：验证邮箱

1. 查收注册邮箱的验证邮件
2. 点击验证链接完成邮箱验证

### 步骤3：完成账户设置

1. 登录SendGrid控制台
2. 完成"Getting Started"向导：
   - 选择集成方式（推荐选择"Web API"）
   - 选择编程语言（Python）
   - 验证发送方身份

## 🔑 获取API密钥

### 创建API Key

1. 登录SendGrid控制台
2. 进入 **Settings** → **API Keys**
3. 点击 **Create API Key**
4. 选择权限级别：
   - **Full Access**：完全权限（推荐开发测试）
   - **Restricted Access**：限制权限（推荐生产环境）
5. 输入API Key名称（如：`my-django-app`）
6. 点击 **Create & View**
7. **重要**：立即复制API Key并保存，页面关闭后无法再查看

### API Key 示例
```
SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

## 📧 验证发送方身份

### 方法1：单一发送方验证（推荐新手）

1. 进入 **Settings** → **Sender Authentication**
2. 点击 **Verify a Single Sender**
3. 填写发送方信息：
   - **From Name**：认知健康团队
   - **From Email**：<EMAIL>（或你的邮箱）
   - **Reply To**：<EMAIL>
   - **Company Address**：填写真实地址
4. 点击 **Create**
5. 查收验证邮件并点击验证链接

### 方法2：域名验证（推荐生产环境）

1. 进入 **Settings** → **Sender Authentication**
2. 点击 **Authenticate Your Domain**
3. 输入你的域名（如：yourdomain.com）
4. 按照指示添加DNS记录
5. 等待DNS验证通过

## 🔧 Django 集成配置

### 方法1：使用 sendgrid-django（推荐）

#### 安装依赖
```bash
pip install sendgrid-django
```

#### 配置 settings.py
```python
# SendGrid 邮件配置
EMAIL_BACKEND = 'sendgrid_backend.SendgridBackend'
SENDGRID_API_KEY = 'SG.your-api-key-here'

# 发送方信息（必须是已验证的发送方）
DEFAULT_FROM_EMAIL = '<EMAIL>'
SERVER_EMAIL = '<EMAIL>'

# 可选：SendGrid 高级配置
SENDGRID_TRACK_EMAIL_OPENS = True
SENDGRID_TRACK_CLICKS_HTML = True
SENDGRID_TRACK_CLICKS_PLAIN = True
```

### 方法2：使用官方 sendgrid-python

#### 安装依赖
```bash
pip install sendgrid
```

#### 配置 settings.py
```python
# SendGrid API配置
SENDGRID_API_KEY = 'SG.your-api-key-here'
DEFAULT_FROM_EMAIL = '<EMAIL>'

# 使用自定义邮件后端
EMAIL_BACKEND = 'path.to.your.sendgrid_backend'
```

### 方法3：使用 django-anymail

#### 安装依赖
```bash
pip install django-anymail[sendgrid]
```

#### 配置 settings.py
```python
# Anymail + SendGrid 配置
EMAIL_BACKEND = 'anymail.backends.sendgrid.EmailBackend'

ANYMAIL = {
    'SENDGRID_API_KEY': 'SG.your-api-key-here',
    'SENDGRID_GENERATE_MESSAGE_ID': True,
    'SENDGRID_MERGE_FIELD_FORMAT': '-{}-',
    'SENDGRID_API_URL': 'https://api.sendgrid.com/v3/',
}

DEFAULT_FROM_EMAIL = '<EMAIL>'
```

## 🔒 安全配置

### 环境变量配置（推荐）

#### 创建 .env 文件
```bash
# .env
SENDGRID_API_KEY=SG.your-api-key-here
DEFAULT_FROM_EMAIL=<EMAIL>
```

#### 修改 settings.py
```python
import os
from dotenv import load_dotenv

load_dotenv()

# SendGrid 配置
EMAIL_BACKEND = 'sendgrid_backend.SendgridBackend'
SENDGRID_API_KEY = os.getenv('SENDGRID_API_KEY')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL')

# 安全检查
if not SENDGRID_API_KEY:
    raise ValueError("SENDGRID_API_KEY environment variable is required")
```

## 📝 测试邮件发送

### 基本测试
```python
# 在 Django shell 中测试
python manage.py shell

from django.core.mail import send_mail

send_mail(
    subject='SendGrid 测试邮件',
    message='这是一封通过SendGrid发送的测试邮件',
    from_email='<EMAIL>',  # 必须是已验证的发送方
    recipient_list=['<EMAIL>'],
    fail_silently=False,
)
```

### HTML邮件测试
```python
from django.core.mail import EmailMultiAlternatives

subject = 'SendGrid HTML 测试'
text_content = '这是纯文本内容'
html_content = '''
<html>
<body>
    <h1>SendGrid 测试</h1>
    <p>这是一封<strong>HTML邮件</strong></p>
    <a href="https://sendgrid.com">访问SendGrid</a>
</body>
</html>
'''

msg = EmailMultiAlternatives(
    subject=subject,
    body=text_content,
    from_email='<EMAIL>',
    to=['<EMAIL>']
)
msg.attach_alternative(html_content, "text/html")
msg.send()
```
