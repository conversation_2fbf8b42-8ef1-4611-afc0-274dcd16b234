#!/usr/bin/env python
"""
Amazon SES 快速配置脚本
自动化配置Amazon SES的部分步骤
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🚀 {title}")
    print("="*60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")
    print("-" * 40)

def check_dependencies():
    """检查依赖包"""
    print_step(1, "检查依赖包")
    
    required_packages = ['boto3', 'django-ses', 'python-dotenv']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装的包: {', '.join(missing_packages)}")
        install = input("是否现在安装? (y/n): ").lower().strip()
        if install == 'y':
            for package in missing_packages:
                print(f"正在安装 {package}...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', package])
        else:
            print("请手动安装依赖包后重新运行此脚本")
            return False
    
    return True

def create_env_file():
    """创建环境变量文件"""
    print_step(2, "创建环境变量文件")
    
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if env_file.exists():
        print("✅ .env 文件已存在")
        overwrite = input("是否覆盖现有配置? (y/n): ").lower().strip()
        if overwrite != 'y':
            return True
    
    # 收集配置信息
    print("\n请输入Amazon SES配置信息:")
    
    aws_access_key = input("AWS Access Key ID: ").strip()
    if not aws_access_key:
        print("❌ Access Key ID 不能为空")
        return False
    
    aws_secret_key = input("AWS Secret Access Key: ").strip()
    if not aws_secret_key:
        print("❌ Secret Access Key 不能为空")
        return False
    
    aws_region = input("AWS SES Region (默认: us-east-1): ").strip() or 'us-east-1'
    default_from_email = input("默认发送方邮箱: ").strip()
    
    if not default_from_email:
        print("❌ 发送方邮箱不能为空")
        return False
    
    # 创建.env文件
    env_content = f"""# Amazon SES 配置
EMAIL_SERVICE=ses

# AWS 凭证
AWS_ACCESS_KEY_ID={aws_access_key}
AWS_SECRET_ACCESS_KEY={aws_secret_key}
AWS_SES_REGION_NAME={aws_region}

# 邮件配置
DEFAULT_FROM_EMAIL={default_from_email}

# Django 配置
DEBUG=True
SECRET_KEY=django-insecure-development-key-change-in-production
"""
    
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print(f"✅ .env 文件已创建")
    return True

def test_aws_connection():
    """测试AWS连接"""
    print_step(3, "测试AWS连接")
    
    try:
        import boto3
        from dotenv import load_dotenv
        
        # 加载环境变量
        load_dotenv()
        
        aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        aws_region = os.getenv('AWS_SES_REGION_NAME', 'us-east-1')
        
        if not aws_access_key or not aws_secret_key:
            print("❌ AWS凭证未配置")
            return False
        
        # 创建SES客户端
        ses_client = boto3.client(
            'ses',
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key,
            region_name=aws_region
        )
        
        # 测试连接
        quota = ses_client.get_send_quota()
        
        print("✅ AWS连接成功!")
        print(f"📊 发送配额信息:")
        print(f"   24小时限制: {quota['Max24HourSend']}")
        print(f"   每秒限制: {quota['MaxSendRate']}")
        print(f"   已发送: {quota['SentLast24Hours']}")
        
        # 检查是否在沙盒模式
        if quota['Max24HourSend'] == 200:
            print("⚠️  当前在沙盒模式，需要申请移出沙盒")
        else:
            print("✅ 已移出沙盒模式")
        
        return True
        
    except Exception as e:
        print(f"❌ AWS连接失败: {e}")
        return False

def check_verified_identities():
    """检查已验证的身份"""
    print_step(4, "检查已验证的身份")
    
    try:
        import boto3
        from dotenv import load_dotenv
        
        load_dotenv()
        
        ses_client = boto3.client(
            'ses',
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_SES_REGION_NAME', 'us-east-1')
        )
        
        # 获取已验证的邮箱
        emails = ses_client.list_verified_email_addresses()
        
        # 获取已验证的域名
        identities = ses_client.list_identities(IdentityType='Domain')
        domains = [i for i in identities['Identities'] if '.' in i]
        
        print("📧 已验证的邮箱地址:")
        if emails['VerifiedEmailAddresses']:
            for email in emails['VerifiedEmailAddresses']:
                print(f"   ✅ {email}")
        else:
            print("   ❌ 没有已验证的邮箱地址")
        
        print("\n🌐 已验证的域名:")
        if domains:
            for domain in domains:
                print(f"   ✅ {domain}")
        else:
            print("   ❌ 没有已验证的域名")
        
        # 检查默认发送方
        default_from = os.getenv('DEFAULT_FROM_EMAIL')
        if default_from:
            print(f"\n📮 默认发送方: {default_from}")
            
            is_verified = False
            if default_from in emails['VerifiedEmailAddresses']:
                is_verified = True
            else:
                domain = default_from.split('@')[1]
                if domain in domains:
                    is_verified = True
            
            if is_verified:
                print("   ✅ 默认发送方已验证")
            else:
                print("   ❌ 默认发送方未验证")
                print("   请在SES控制台验证此邮箱或域名")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_email_sending():
    """测试邮件发送"""
    print_step(5, "测试邮件发送")
    
    test_email = input("请输入测试邮箱地址 (回车跳过): ").strip()
    if not test_email:
        print("跳过邮件发送测试")
        return True
    
    try:
        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        
        import django
        django.setup()
        
        from django.core.mail import send_mail
        from django.conf import settings
        
        result = send_mail(
            subject='Amazon SES 配置测试',
            message='这是一封测试邮件，用于验证Amazon SES配置是否正确。\n\n如果您收到此邮件，说明配置成功！',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[test_email],
            fail_silently=False,
        )
        
        if result == 1:
            print("✅ 测试邮件发送成功!")
            print(f"   发送方: {settings.DEFAULT_FROM_EMAIL}")
            print(f"   接收方: {test_email}")
            print("   请检查邮箱是否收到邮件")
        else:
            print("❌ 测试邮件发送失败")
        
        return result == 1
        
    except Exception as e:
        print(f"❌ 邮件发送失败: {e}")
        return False

def generate_summary():
    """生成配置摘要"""
    print_step(6, "配置摘要")
    
    from dotenv import load_dotenv
    load_dotenv()
    
    print("📋 当前配置:")
    print(f"   邮件服务: {os.getenv('EMAIL_SERVICE', '未设置')}")
    print(f"   AWS区域: {os.getenv('AWS_SES_REGION_NAME', '未设置')}")
    print(f"   发送方邮箱: {os.getenv('DEFAULT_FROM_EMAIL', '未设置')}")
    print(f"   Access Key: {'已设置' if os.getenv('AWS_ACCESS_KEY_ID') else '未设置'}")
    print(f"   Secret Key: {'已设置' if os.getenv('AWS_SECRET_ACCESS_KEY') else '未设置'}")
    
    print("\n📚 下一步操作:")
    print("1. 如果在沙盒模式，请在SES控制台申请移出沙盒")
    print("2. 验证更多发送方身份（邮箱或域名）")
    print("3. 配置DKIM签名提高送达率")
    print("4. 设置退信和投诉处理")
    print("5. 监控发送统计和成本")
    
    print("\n🔗 有用链接:")
    print("- SES控制台: https://console.aws.amazon.com/ses/")
    print("- 申请移出沙盒: SES控制台 → Account dashboard → Request production access")
    print("- 验证身份: SES控制台 → Verified identities → Create identity")

def main():
    """主函数"""
    print_header("Amazon SES 快速配置向导")
    
    print("此脚本将帮助您快速配置Amazon SES邮件服务")
    print("请确保您已经:")
    print("1. 注册了AWS账户")
    print("2. 创建了IAM用户并获取了访问密钥")
    print("3. 在SES控制台验证了发送方身份")
    
    continue_setup = input("\n是否继续配置? (y/n): ").lower().strip()
    if continue_setup != 'y':
        print("配置已取消")
        return
    
    # 执行配置步骤
    steps = [
        ("检查依赖", check_dependencies),
        ("配置环境变量", create_env_file),
        ("测试AWS连接", test_aws_connection),
        ("检查验证身份", check_verified_identities),
        ("测试邮件发送", test_email_sending),
        ("生成摘要", generate_summary),
    ]
    
    results = []
    for step_name, step_func in steps:
        try:
            result = step_func()
            results.append((step_name, result))
            if not result and step_name in ["检查依赖", "配置环境变量", "测试AWS连接"]:
                print(f"\n❌ {step_name}失败，无法继续")
                break
        except KeyboardInterrupt:
            print("\n\n⏹️  配置被用户中断")
            break
        except Exception as e:
            print(f"\n❌ {step_name}出现异常: {e}")
            results.append((step_name, False))
    
    # 显示结果
    print_header("配置结果")
    for step_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{step_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    if success_count >= 3:  # 至少前3步成功
        print(f"\n🎉 基础配置完成! ({success_count}/{total_count})")
        print("您现在可以使用Amazon SES发送邮件了!")
    else:
        print(f"\n⚠️  配置未完成 ({success_count}/{total_count})")
        print("请检查错误信息并重新配置")

if __name__ == "__main__":
    main()
