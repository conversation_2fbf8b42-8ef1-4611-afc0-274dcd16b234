#!/usr/bin/env python
"""
测试重复请求修复
"""
import requests
import json
import time

def test_single_registration_no_duplicates():
    """测试单次注册不会产生重复请求"""
    print("🧪 测试单次注册（检查是否有重复请求）...")
    
    api_url = "http://127.0.0.1:8001/api/register/"
    test_email = f"no_duplicate_test_{int(time.time())}@example.com"
    
    user_data = {
        "email": test_email,
        "password": "testpass123",
        "password_confirm": "testpass123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    headers = {'Content-Type': 'application/json'}
    
    try:
        print(f"📝 注册邮箱: {test_email}")
        response = requests.post(api_url, json=user_data, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 201:
            response_data = response.json()
            if response_data.get('success'):
                print("✅ 注册成功")
                
                # 等待2秒，然后检查是否有重复用户
                time.sleep(2)
                
                # 尝试再次注册相同邮箱，应该失败
                print("📝 验证邮箱唯一性...")
                response2 = requests.post(api_url, json=user_data, headers=headers)
                print(f"验证请求状态码: {response2.status_code}")
                print(f"验证请求响应: {response2.json()}")
                
                if response2.status_code == 400:
                    print("✅ 邮箱唯一性验证通过")
                    return True
                else:
                    print("❌ 邮箱唯一性验证失败")
                    return False
            else:
                print("❌ 响应状态码201但success=False")
                return False
        else:
            print("❌ 注册失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def monitor_backend_logs():
    """提示用户监控后端日志"""
    print("\n📊 请检查后端服务器日志:")
    print("   - 应该只看到一个201成功请求")
    print("   - 不应该看到紧接着的400错误请求")
    print("   - 不应该看到'Registration validation error'")
    print("   - 应该只看到一次'验证邮件已发送'")

def test_form_submission_simulation():
    """模拟表单提交，检查是否有重复请求"""
    print("\n🧪 模拟表单提交测试...")
    
    api_url = "http://127.0.0.1:8001/api/register/"
    test_email = f"form_test_{int(time.time())}@example.com"
    
    user_data = {
        "email": test_email,
        "password": "testpass123",
        "password_confirm": "testpass123",
        "first_name": "Form",
        "last_name": "Test"
    }
    
    headers = {'Content-Type': 'application/json'}
    
    try:
        print(f"📝 模拟表单提交: {test_email}")
        
        # 模拟快速连续的两个请求（模拟重复事件绑定的情况）
        import threading
        import time
        
        results = []
        
        def make_request(request_id):
            try:
                # 添加小的延迟来模拟真实情况
                time.sleep(0.01 * request_id)
                response = requests.post(api_url, json=user_data, headers=headers)
                results.append({
                    'id': request_id,
                    'status_code': response.status_code,
                    'response': response.json(),
                    'timestamp': time.time()
                })
            except Exception as e:
                results.append({
                    'id': request_id,
                    'error': str(e),
                    'timestamp': time.time()
                })
        
        # 创建两个线程模拟重复提交
        threads = []
        for i in range(2):
            thread = threading.Thread(target=make_request, args=(i+1,))
            threads.append(thread)
        
        # 几乎同时启动
        for thread in threads:
            thread.start()
        
        # 等待完成
        for thread in threads:
            thread.join()
        
        # 分析结果
        print("模拟表单提交结果:")
        success_count = 0
        for result in results:
            request_id = result['id']
            if 'error' in result:
                print(f"  请求{request_id}: ❌ 错误 - {result['error']}")
            else:
                status_code = result['status_code']
                response_data = result['response']
                success = status_code == 201 and response_data.get('success')
                status = "✅ 成功" if success else "❌ 失败"
                print(f"  请求{request_id}: {status} (状态码: {status_code})")
                
                if success:
                    success_count += 1
        
        if success_count == 1:
            print("✅ 重复提交防护正常：只有一个请求成功")
            return True
        elif success_count == 0:
            print("❌ 所有请求都失败了")
            return False
        else:
            print(f"❌ 重复提交防护失败：{success_count}个请求成功")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试重复请求修复...")
    print("=" * 60)
    
    tests = [
        ("单次注册无重复", test_single_registration_no_duplicates),
        ("表单提交模拟", test_form_submission_simulation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    monitor_backend_logs()
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 重复请求问题已修复！")
        print("💡 现在前端应该不会再发送重复的注册请求")
    else:
        print("⚠️  部分测试失败，可能仍有重复请求问题")

if __name__ == "__main__":
    main()
