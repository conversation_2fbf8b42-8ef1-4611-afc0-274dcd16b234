-- SQL script to manually create api_customuser table
-- This can be used as a backup if Django migrations fail

-- Create the api_customuser table
CREATE TABLE IF NOT EXISTS api_customuser (
    id TEXT PRIMARY KEY,
    password VARCHAR(128) NOT NULL,
    last_login DATETIME,
    is_superuser BOOLEAN NOT NULL DEFAULT 0,
    email VARCHAR(254) UNIQUE NOT NULL,
    first_name VARCHAR(30) NOT NULL DEFAULT '',
    last_name VARCHAR(30) NOT NULL DEFAULT '',
    is_active BOOLEAN NOT NULL DEFAULT 0,
    is_staff BOOLEAN NOT NULL DEFAULT 0,
    date_joined DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    avatar VARCHAR(100)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS api_customuser_email_idx ON api_customuser(email);
CREATE INDEX IF NOT EXISTS api_customuser_is_active_idx ON api_customuser(is_active);

-- Create the many-to-many tables for groups and permissions
CREATE TABLE IF NOT EXISTS api_customuser_groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customuser_id TEXT NOT NULL,
    group_id INTEGER NOT NULL,
    FOREIGN KEY (customuser_id) REFERENCES api_customuser(id),
    FOREIGN KEY (group_id) REFERENCES auth_group(id),
    UNIQUE(customuser_id, group_id)
);

CREATE TABLE IF NOT EXISTS api_customuser_user_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customuser_id TEXT NOT NULL,
    permission_id INTEGER NOT NULL,
    FOREIGN KEY (customuser_id) REFERENCES api_customuser(id),
    FOREIGN KEY (permission_id) REFERENCES auth_permission(id),
    UNIQUE(customuser_id, permission_id)
);

-- Create related tables
CREATE TABLE IF NOT EXISTS api_emailverificationtoken (
    id TEXT PRIMARY KEY,
    token VARCHAR(255) UNIQUE NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT 0,
    user_id TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES api_customuser(id)
);

CREATE TABLE IF NOT EXISTS api_passwordresettoken (
    id TEXT PRIMARY KEY,
    token VARCHAR(255) UNIQUE NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT 0,
    user_id TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES api_customuser(id)
);

CREATE TABLE IF NOT EXISTS api_audioanalysis (
    id TEXT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    audio_file VARCHAR(100) NOT NULL,
    result TEXT,
    upload_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    relationship VARCHAR(50),
    occupation VARCHAR(100),
    age INTEGER,
    date_of_birth VARCHAR(20),
    user_id TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES api_customuser(id)
);

-- Create indexes for related tables
CREATE INDEX IF NOT EXISTS api_emailverificationtoken_user_id_idx ON api_emailverificationtoken(user_id);
CREATE INDEX IF NOT EXISTS api_passwordresettoken_user_id_idx ON api_passwordresettoken(user_id);
CREATE INDEX IF NOT EXISTS api_audioanalysis_user_id_idx ON api_audioanalysis(user_id);
CREATE INDEX IF NOT EXISTS api_audioanalysis_upload_time_idx ON api_audioanalysis(upload_time);

-- Insert Django migration record to mark these migrations as applied
INSERT OR IGNORE INTO django_migrations (app, name, applied) VALUES 
('api', '0001_initial', CURRENT_TIMESTAMP),
('api', '0002_add_related_models', CURRENT_TIMESTAMP),
('api', '0003_update_customuser_manager', CURRENT_TIMESTAMP);
