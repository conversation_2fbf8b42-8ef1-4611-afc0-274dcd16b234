#!/usr/bin/env python
"""
测试用户名生成功能
"""

import os
import sys
import django
from pathlib import Path

# 添加项目路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from api.models import CustomUser, generate_unique_username
from api.serializers import UserRegistrationSerializer


def test_username_generation():
    """测试用户名生成功能"""
    print("🧪 测试用户名生成功能")
    print("=" * 50)
    
    # 测试用例
    test_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '中文用户@example.com',
        '<EMAIL>',
        '<EMAIL>',  # 极短邮箱
    ]
    
    print("📋 测试邮箱地址和生成的用户名:")
    for email in test_emails:
        try:
            username = generate_unique_username(email)
            print(f"  {email:<30} → {username}")
        except Exception as e:
            print(f"  {email:<30} → 错误: {e}")
    
    print("\n" + "=" * 50)


def test_user_registration():
    """测试用户注册时的用户名自动生成"""
    print("👤 测试用户注册时的用户名自动生成")
    print("=" * 50)
    
    # 测试用户数据
    test_user_data = {
        'email': '<EMAIL>',
        'password': 'testpassword123',
        'password_confirm': 'testpassword123',
        'first_name': 'Test',
        'last_name': 'User'
    }
    
    try:
        # 删除可能存在的测试用户
        CustomUser.objects.filter(email=test_user_data['email']).delete()
        
        # 创建用户
        serializer = UserRegistrationSerializer(data=test_user_data)
        if serializer.is_valid():
            user = serializer.save()
            print(f"✅ 用户创建成功:")
            print(f"   邮箱: {user.email}")
            print(f"   用户名: {user.username}")
            print(f"   全名: {user.full_name}")
            print(f"   显示名: {user.display_name}")
            
            # 清理测试数据
            user.delete()
            print("🗑️  测试用户已删除")
            
        else:
            print(f"❌ 用户创建失败: {serializer.errors}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)


def test_duplicate_username_handling():
    """测试重复用户名处理"""
    print("🔄 测试重复用户名处理")
    print("=" * 50)
    
    base_email = '<EMAIL>'
    
    try:
        # 删除可能存在的测试用户
        CustomUser.objects.filter(email__contains='<EMAIL>').delete()
        
        # 创建多个相似邮箱的用户
        similar_emails = [
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>',
            '<EMAIL>',  # 重复邮箱（应该失败）
        ]
        
        created_users = []
        
        for i, email in enumerate(similar_emails):
            try:
                user_data = {
                    'email': email,
                    'password': 'testpassword123',
                    'password_confirm': 'testpassword123',
                    'first_name': f'User{i+1}',
                    'last_name': 'Test'
                }
                
                serializer = UserRegistrationSerializer(data=user_data)
                if serializer.is_valid():
                    user = serializer.save()
                    created_users.append(user)
                    print(f"✅ 用户 {i+1}: {email} → 用户名: {user.username}")
                else:
                    print(f"❌ 用户 {i+1}: {email} → 创建失败: {serializer.errors}")
                    
            except Exception as e:
                print(f"❌ 用户 {i+1}: {email} → 异常: {e}")
        
        print(f"\n📊 成功创建 {len(created_users)} 个用户")
        
        # 清理测试数据
        for user in created_users:
            user.delete()
        print("🗑️  所有测试用户已删除")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)


def main():
    """主测试函数"""
    print("🚀 用户名生成功能测试")
    print("=" * 60)
    
    try:
        # 运行所有测试
        test_username_generation()
        test_user_registration()
        test_duplicate_username_handling()
        
        print("🎉 所有测试完成!")
        print("\n📋 测试总结:")
        print("✅ 用户名生成算法正常")
        print("✅ 用户注册时自动生成用户名")
        print("✅ 重复用户名处理正常")
        print("✅ 现在用户上传音频应该不会因为缺少用户名而出错")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")


if __name__ == "__main__":
    main()
