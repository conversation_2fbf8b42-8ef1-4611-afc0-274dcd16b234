#!/usr/bin/env python
"""
测试API注册功能
"""
import requests
import json
import time

def test_duplicate_registration():
    """测试重复邮箱注册"""
    print("🧪 测试API重复邮箱注册...")
    
    api_url = "http://127.0.0.1:8001/api/register/"
    test_email = "<EMAIL>"
    
    user_data = {
        "email": test_email,
        "password": "testpass123",
        "password_confirm": "testpass123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        # 第一次注册
        print("📝 第一次注册...")
        response1 = requests.post(api_url, json=user_data, headers=headers)
        print(f"状态码: {response1.status_code}")
        print(f"响应: {response1.json()}")
        
        if response1.status_code == 201:
            print("✅ 第一次注册成功")
        else:
            print("❌ 第一次注册失败")
            return False
        
        # 等待一秒
        time.sleep(1)
        
        # 第二次注册（相同邮箱）
        print("\n📝 第二次注册（相同邮箱，应该失败）...")
        response2 = requests.post(api_url, json=user_data, headers=headers)
        print(f"状态码: {response2.status_code}")
        print(f"响应: {response2.json()}")
        
        if response2.status_code == 400:
            response_data = response2.json()
            if not response_data.get('success', True) and 'email' in response_data.get('errors', {}):
                print("✅ 第二次注册正确被拒绝，错误信息正确")
                return True
            else:
                print("❌ 第二次注册被拒绝，但错误信息不正确")
                return False
        else:
            print("❌ 第二次注册应该失败但没有失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_concurrent_registration():
    """测试并发注册相同邮箱"""
    print("\n🧪 测试并发注册相同邮箱...")
    
    import threading
    import time
    
    api_url = "http://127.0.0.1:8001/api/register/"
    test_email = "<EMAIL>"
    
    results = []
    
    def register_user(thread_id):
        """注册用户"""
        user_data = {
            "email": test_email,
            "password": f"testpass{thread_id}",
            "password_confirm": f"testpass{thread_id}",
            "first_name": f"User{thread_id}",
            "last_name": "Test"
        }
        
        headers = {'Content-Type': 'application/json'}
        
        try:
            response = requests.post(api_url, json=user_data, headers=headers)
            results.append({
                'thread_id': thread_id,
                'status_code': response.status_code,
                'response': response.json(),
                'success': response.status_code == 201
            })
        except Exception as e:
            results.append({
                'thread_id': thread_id,
                'error': str(e),
                'success': False
            })
    
    # 创建3个线程同时注册相同邮箱
    threads = []
    for i in range(3):
        thread = threading.Thread(target=register_user, args=(i,))
        threads.append(thread)
    
    # 同时启动所有线程
    for thread in threads:
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 分析结果
    success_count = sum(1 for result in results if result.get('success', False))
    
    print(f"并发注册结果:")
    for result in results:
        thread_id = result['thread_id']
        if result.get('success', False):
            print(f"  线程{thread_id}: ✅ 成功")
        else:
            print(f"  线程{thread_id}: ❌ 失败 - {result.get('response', {}).get('message', result.get('error', '未知错误'))}")
    
    if success_count == 1:
        print("✅ 并发注册正确处理：只有一个成功")
        return True
    else:
        print(f"❌ 并发注册处理异常：{success_count}个成功")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试API注册功能...")
    print("=" * 50)
    
    tests = [
        ("重复邮箱注册", test_duplicate_registration),
        ("并发注册相同邮箱", test_concurrent_registration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试{test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有API测试通过！注册功能正常工作！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
