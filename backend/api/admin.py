from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from .models import CustomUser, EmailVerificationToken, PasswordResetToken, AudioAnalysis


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """
    自定义用户管理界面
    """
    list_display = ['email', 'display_name', 'is_active', 'is_staff', 'date_joined']
    list_filter = ['is_active', 'is_staff', 'is_superuser', 'date_joined']
    search_fields = ['email', 'first_name', 'last_name']
    ordering = ['-date_joined']

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        ('个人信息', {'fields': ('first_name', 'last_name', 'avatar')}),
        ('权限', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('重要日期', {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2'),
        }),
    )

    readonly_fields = ['date_joined', 'last_login']


@admin.register(EmailVerificationToken)
class EmailVerificationTokenAdmin(admin.ModelAdmin):
    """
    邮箱验证令牌管理界面
    """
    list_display = ['user_email', 'created_at', 'expires_at', 'is_used', 'is_expired_display']
    list_filter = ['is_used', 'created_at', 'expires_at']
    search_fields = ['user__email', 'token']
    readonly_fields = ['token', 'created_at', 'expires_at']

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def is_expired_display(self, obj):
        if obj.is_expired():
            return format_html('<span style="color: red;">已过期</span>')
        return format_html('<span style="color: green;">有效</span>')
    is_expired_display.short_description = '状态'


@admin.register(PasswordResetToken)
class PasswordResetTokenAdmin(admin.ModelAdmin):
    """
    密码重置令牌管理界面
    """
    list_display = ['user_email', 'created_at', 'expires_at', 'is_used', 'is_expired_display']
    list_filter = ['is_used', 'created_at', 'expires_at']
    search_fields = ['user__email', 'token']
    readonly_fields = ['token', 'created_at', 'expires_at']

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def is_expired_display(self, obj):
        if obj.is_expired():
            return format_html('<span style="color: red;">已过期</span>')
        return format_html('<span style="color: green;">有效</span>')
    is_expired_display.short_description = '状态'


@admin.register(AudioAnalysis)
class AudioAnalysisAdmin(admin.ModelAdmin):
    """
    音频分析管理界面
    """
    list_display = ['user_email', 'filename', 'status', 'upload_time', 'relationship', 'age']
    list_filter = ['status', 'upload_time', 'relationship']
    search_fields = ['user__email', 'filename']
    readonly_fields = ['id', 'upload_time']

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'user', 'filename', 'audio_file', 'upload_time')
        }),
        ('分析结果', {
            'fields': ('status', 'result')
        }),
        ('说话人信息', {
            'fields': ('relationship', 'occupation', 'age', 'date_of_birth')
        }),
    )

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = '用户邮箱'