#!/usr/bin/env python3
"""
测试重新编写的单词上色功能 - 使用自定义Flowable实现
"""

import json
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Flowable
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfgen import canvas
from io import BytesIO
import re

# 模拟测试数据 - 包含丰富的彩色转录文本
test_data = {
    "filename": "test_new_colored_transcription.wav",
    "age": "68",
    "relationship": "my_self",
    "occupation": "retired",
    "uploaded_at": "2025-01-17T10:30:00Z",
    "result": json.dumps({
        "Predicted mmse score": 23.2,
        "Percentile": 70,
        "Model": "Advanced Neural Network",
        "Model performance": {
            "RMSE": 2.28,
            "Pearson correlation coefficient": 0.89
        },
        "Transcribed": "The quick brown fox jumps over the lazy dog. Um, this is a test transcription with various speech patterns.",
        "Transcribed with color": '<span class="word-normal">The quick brown fox</span> <span class="word-hesitation">jumps</span> <span class="word-normal">over the</span> <span class="word-repetition">lazy lazy</span> <span class="word-normal">dog.</span> <span class="word-filler">Um,</span> <span class="word-normal">this is a test transcription with</span> <span class="word-hesitation">various</span> <span class="word-normal">speech</span> <span class="word-pause">[pause]</span> <span class="word-normal">patterns including</span> <span class="word-prolongation">looong</span> <span class="word-normal">words and</span> <span class="word-unclear">[unclear]</span> <span class="word-normal">sections.</span>',
        "Selected features": {
            "digipsych_prosody_feats": [
                {
                    "feature name": "speaking_rate",
                    "value": 0.00132,
                    "clsi lower": 0.001,
                    "clsi upper": 0.002,
                    "brief introduction": "Rate of speech measured in syllables per second"
                }
            ]
        }
    })
}

class ColoredTranscriptionFlowable(Flowable):
    """自定义Flowable类用于渲染彩色转录文本"""
    
    def __init__(self, transcribed_with_color, width, height):
        Flowable.__init__(self)
        self.transcribed_with_color = transcribed_with_color
        self.width = width
        self.height = height
        
        # 定义颜色映射 - 与详情页完全一致
        self.color_mapping = {
            'word-normal': colors.Color(0.2, 0.2, 0.2),  # #333333
            'word-hesitation': colors.Color(1.0, 0.42, 0.21),  # #ff6b35
            'word-repetition': colors.Color(0.91, 0.30, 0.24),  # #e74c3c
            'word-pause': colors.Color(0.61, 0.35, 0.71),  # #9b59b6
            'word-filler': colors.Color(0.95, 0.61, 0.07),  # #f39c12
            'word-prolongation': colors.Color(0.20, 0.60, 0.86),  # #3498db
            'word-unclear': colors.Color(0.58, 0.65, 0.65)  # #95a5a6
        }
        
        # 解析HTML标签
        self.parsed_words = self._parse_html_tags()
    
    def _parse_html_tags(self):
        """解析HTML标签，提取单词和样式信息"""
        if not self.transcribed_with_color:
            return [{'text': 'Transcription not available', 'style': 'word-normal'}]
        
        # 使用正则表达式解析span标签
        pattern = r'<span class="(word-[^"]*)">(.*?)</span>'
        matches = re.findall(pattern, self.transcribed_with_color, re.IGNORECASE)
        
        parsed_words = []
        last_end = 0
        
        for match in re.finditer(pattern, self.transcribed_with_color, re.IGNORECASE):
            # 添加标签前的普通文本
            if match.start() > last_end:
                plain_text = self.transcribed_with_color[last_end:match.start()]
                if plain_text.strip():
                    parsed_words.append({'text': plain_text, 'style': 'word-normal'})
            
            # 添加标签内的文本
            class_name = match.group(1)
            text_content = match.group(2)
            parsed_words.append({'text': text_content, 'style': class_name})
            
            last_end = match.end()
        
        # 添加最后剩余的文本
        if last_end < len(self.transcribed_with_color):
            remaining_text = self.transcribed_with_color[last_end:]
            if remaining_text.strip():
                parsed_words.append({'text': remaining_text, 'style': 'word-normal'})
        
        return parsed_words if parsed_words else [{'text': self.transcribed_with_color, 'style': 'word-normal'}]
    
    def draw(self):
        """绘制彩色文本"""
        canvas_obj = self.canv
        
        # 设置字体
        font_name = 'Times-Italic'
        font_size = 10
        canvas_obj.setFont(font_name, font_size)
        
        # 起始位置
        x = 16  # 左边距
        y = self.height - 20  # 从顶部开始
        line_height = 14
        max_width = self.width - 32  # 减去左右边距
        
        current_x = x
        current_y = y
        
        for word_info in self.parsed_words:
            text = word_info['text']
            style = word_info['style']
            
            # 设置颜色
            color = self.color_mapping.get(style, self.color_mapping['word-normal'])
            canvas_obj.setFillColor(color)
            
            # 处理换行
            words = text.split()
            for word in words:
                word_width = canvas_obj.stringWidth(word + ' ', font_name, font_size)
                
                # 检查是否需要换行
                if current_x + word_width > max_width and current_x > x:
                    current_x = x
                    current_y -= line_height
                
                # 绘制单词
                if style == 'word-hesitation':
                    # 犹豫词：加粗效果（通过重复绘制实现）
                    canvas_obj.drawString(current_x, current_y, word)
                    canvas_obj.drawString(current_x + 0.3, current_y, word)
                elif style == 'word-repetition':
                    # 重复词：加粗+下划线效果
                    canvas_obj.drawString(current_x, current_y, word)
                    canvas_obj.drawString(current_x + 0.3, current_y, word)
                    # 绘制下划线
                    canvas_obj.line(current_x, current_y - 2, current_x + word_width - 4, current_y - 2)
                elif style == 'word-pause':
                    # 停顿：加粗效果
                    canvas_obj.drawString(current_x, current_y, word)
                    canvas_obj.drawString(current_x + 0.3, current_y, word)
                else:
                    # 其他样式：正常绘制
                    canvas_obj.drawString(current_x, current_y, word)
                
                current_x += word_width
                
                # 如果到达行尾，换行
                if current_x >= max_width:
                    current_x = x
                    current_y -= line_height

def main():
    try:
        print("正在测试重新编写的单词上色功能...")
        
        # 解析测试数据
        analysis_result = json.loads(test_data['result'])
        
        # 创建PDF缓冲区
        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer, 
            pagesize=A4, 
            rightMargin=60, 
            leftMargin=60, 
            topMargin=60, 
            bottomMargin=60
        )

        # 获取样式
        styles = getSampleStyleSheet()
        
        # 自定义样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=20,
            spaceAfter=12,
            spaceBefore=0,
            alignment=TA_CENTER,
            fontName='Times-Bold',
            textColor=colors.black
        )
        
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=24,
            spaceBefore=0,
            alignment=TA_CENTER,
            fontName='Times-Bold',
            textColor=colors.black
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=8,
            spaceBefore=16,
            fontName='Times-Bold',
            textColor=colors.black,
            alignment=TA_LEFT
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=10,
            fontName='Times-Roman',
            textColor=colors.black,
            alignment=TA_LEFT,
            spaceAfter=6
        )

        # 构建PDF内容
        story = []

        # 标题
        story.append(Paragraph("Cognitive Health Speech Analysis", title_style))
        story.append(Paragraph("CONFIDENTIAL SCIENTIFIC REPORT", subtitle_style))
        story.append(Spacer(1, 16))

        # 转录文本 - 使用自定义Flowable实现单词上色
        story.append(Paragraph("Speech Analysis", heading_style))
        
        # 子标题样式
        sub_heading_style = ParagraphStyle(
            'SubHeading',
            parent=normal_style,
            fontSize=11,
            fontName='Times-Bold',
            spaceAfter=8,
            spaceBefore=4
        )
        story.append(Paragraph("Enhanced Transcription with Linguistic Analysis", sub_heading_style))
        
        # 获取转录文本
        transcribed_with_color = analysis_result.get('Transcribed with color', '')
        transcribed = analysis_result.get('Transcribed', 'Transcription not available')
        
        # 使用彩色转录文本，如果没有则使用普通转录
        transcription_text = transcribed_with_color if transcribed_with_color.strip() else transcribed
        
        # 创建转录文本框背景
        transcription_bg_data = [['']]
        transcription_bg_table = Table(transcription_bg_data, colWidths=[6.4*inch], rowHeights=[2*inch])
        transcription_bg_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.Color(0.97, 0.97, 0.97)),  # 浅灰色背景
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
            ('TOPPADDING', (0, 0), (-1, -1), 0),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
            ('LINEBELOW', (0, 0), (-1, -1), 4, colors.black),  # 左边框效果
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP')
        ]))
        
        story.append(transcription_bg_table)
        
        # 添加彩色转录文本Flowable
        colored_transcription = ColoredTranscriptionFlowable(transcription_text, 6.4*inch, 2*inch)
        story.append(colored_transcription)
        
        # 添加颜色说明
        story.append(Spacer(1, 12))
        legend_style = ParagraphStyle(
            'Legend',
            parent=normal_style,
            fontSize=9,
            fontName='Times-Roman',
            spaceAfter=8
        )
        
        story.append(Paragraph("<b>Color Legend:</b>", legend_style))
        legend_text = """<font color="#333333">■ Normal speech</font> | 
<font color="#ff6b35">■ Hesitations</font> | 
<font color="#e74c3c">■ Repetitions</font> | 
<font color="#9b59b6">■ Pauses</font> | 
<font color="#f39c12">■ Fillers</font> | 
<font color="#3498db">■ Prolongations</font> | 
<font color="#95a5a6">■ Unclear</font>"""
        
        story.append(Paragraph(legend_text, legend_style))
        story.append(Spacer(1, 20))

        # 生成PDF
        doc.build(story)
        
        # 保存PDF文件
        pdf_bytes = buffer.getvalue()
        buffer.close()
        
        with open('test_new_colored_transcription.pdf', 'wb') as f:
            f.write(pdf_bytes)
        
        print("✅ 重新编写的单词上色功能测试成功!")
        print(f"📄 文件大小: {len(pdf_bytes)} bytes")
        print("📁 文件保存为: test_new_colored_transcription.pdf")
        
        # 验证文件是否存在
        import os
        if os.path.exists('test_new_colored_transcription.pdf'):
            file_size = os.path.getsize('test_new_colored_transcription.pdf')
            print(f"✅ 文件验证成功，大小: {file_size} bytes")
            print("🎨 新的单词上色功能特点：")
            print("   🔧 使用自定义Flowable类")
            print("   🎯 直接在Canvas上绘制彩色文本")
            print("   🌈 支持所有7种单词类型的颜色")
            print("   💪 加粗效果（犹豫词、重复词、停顿）")
            print("   📏 下划线效果（重复词）")
            print("   📝 与详情页颜色完全一致")
            print("   🔄 自动换行和布局")
        else:
            print("❌ 文件验证失败")

    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    print("\n测试完成。如果成功，应该生成了带有真正单词上色的PDF文件。")
