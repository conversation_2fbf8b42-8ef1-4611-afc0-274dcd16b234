<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .status {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        button:hover { background: #0056b3; }
        .cta-button {
            background: #17a2b8;
            font-size: 1.1rem;
            padding: 1rem 2rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Authentication Test</h1>
        
        <h2>Token Status</h2>
        <div id="token-status" class="status warning">Checking token...</div>
        <pre id="token-details"></pre>

        <h2>Authentication Test</h2>
        <div id="auth-status" class="status warning">Testing authentication...</div>
        <pre id="auth-details"></pre>

        <h2>Start Screening Test</h2>
        <button type="button" onclick="testStartScreening()" class="cta-button">Test Start Screening</button>
        <div id="screening-status" class="status warning">Ready to test</div>
        <pre id="screening-details"></pre>

        <h2>Actions</h2>
        <button onclick="clearToken()">Clear Token</button>
        <button onclick="setTestToken()">Set Test Token</button>
        <button onclick="window.location.href='/'">Go to Home</button>
        <button onclick="window.location.href='/login/'">Go to Login</button>
    </div>

    <script>
        // 使用Django模板变量传递配置
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        
        // 备用配置
        if (!window.API_BASE_URL || window.API_BASE_URL === '') {
            window.API_BASE_URL = "http://**************:8001";
        }
        if (!window.LOCAL_BASE_URL || window.LOCAL_BASE_URL === '') {
            window.LOCAL_BASE_URL = "http://**************:8000";
        }

        // 复制主页的认证函数
        function isUserAuthenticated() {
            const token = localStorage.getItem('access_token');
            console.log('🔍 Checking authentication - token exists:', !!token);
            
            if (!token) {
                console.log('❌ No token found');
                return false;
            }

            try {
                // Simple JWT decode (without verification)
                const payload = JSON.parse(atob(token.split('.')[1]));
                const now = Date.now() / 1000;
                
                console.log('🔍 Token payload:', {
                    exp: payload.exp,
                    now: now,
                    expired: payload.exp < now,
                    user_id: payload.user_id,
                    email: payload.email
                });

                // Check if token is expired
                if (payload.exp < now) {
                    console.log('❌ Token expired, removing from localStorage');
                    localStorage.removeItem('access_token');
                    return false;
                }

                console.log('✅ Token is valid');
                return true;
            } catch (error) {
                console.error('❌ Error checking authentication:', error);
                localStorage.removeItem('access_token');
                return false;
            }
        }

        function startScreening() {
            console.log('🔄 Start screening button clicked');
            
            // 添加更详细的调试信息
            const token = localStorage.getItem('access_token');
            console.log('🔍 Token check:', {
                hasToken: !!token,
                tokenLength: token ? token.length : 0,
                tokenPreview: token ? token.substring(0, 20) + '...' : 'null'
            });

            const isAuth = isUserAuthenticated();
            console.log('🔍 Authentication result:', isAuth);

            if (isAuth) {
                console.log('✅ User is authenticated, redirecting to audio upload');
                window.location.href = '/audio_upload/';
            } else {
                console.log('⚠️ User not authenticated, redirecting to login');
                // Store the intended destination
                sessionStorage.setItem('redirectAfterLogin', '/audio_upload/');
                console.log('💾 Stored redirect URL in sessionStorage');
                window.location.href = '/login/';
            }
        }

        function testStartScreening() {
            const status = document.getElementById('screening-status');
            const details = document.getElementById('screening-details');
            
            status.className = 'status warning';
            status.textContent = '🔄 Testing start screening logic...';
            
            try {
                startScreening();
                status.className = 'status success';
                status.textContent = '✅ Start screening function executed';
                details.textContent = 'Check console for detailed logs';
            } catch (error) {
                status.className = 'status error';
                status.textContent = '❌ Error in start screening function';
                details.textContent = error.message;
            }
        }

        function checkTokenStatus() {
            const tokenStatus = document.getElementById('token-status');
            const tokenDetails = document.getElementById('token-details');
            
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                tokenStatus.className = 'status error';
                tokenStatus.textContent = '❌ No token found';
                tokenDetails.textContent = 'localStorage.getItem("access_token") returned null';
                return;
            }

            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const now = Date.now() / 1000;
                
                const tokenInfo = {
                    hasToken: true,
                    tokenLength: token.length,
                    payload: {
                        user_id: payload.user_id,
                        email: payload.email,
                        exp: payload.exp,
                        iat: payload.iat
                    },
                    currentTime: now,
                    expired: payload.exp < now,
                    timeUntilExpiry: payload.exp - now
                };
                
                tokenDetails.textContent = JSON.stringify(tokenInfo, null, 2);
                
                if (payload.exp < now) {
                    tokenStatus.className = 'status error';
                    tokenStatus.textContent = '❌ Token expired';
                } else {
                    tokenStatus.className = 'status success';
                    tokenStatus.textContent = '✅ Token valid';
                }
            } catch (error) {
                tokenStatus.className = 'status error';
                tokenStatus.textContent = '❌ Invalid token format';
                tokenDetails.textContent = error.message;
            }
        }

        function checkAuthentication() {
            const authStatus = document.getElementById('auth-status');
            const authDetails = document.getElementById('auth-details');
            
            const result = isUserAuthenticated();
            
            if (result) {
                authStatus.className = 'status success';
                authStatus.textContent = '✅ User is authenticated';
            } else {
                authStatus.className = 'status error';
                authStatus.textContent = '❌ User is not authenticated';
            }
            
            authDetails.textContent = `isUserAuthenticated() returned: ${result}`;
        }

        function clearToken() {
            localStorage.removeItem('access_token');
            console.log('🗑️ Token cleared');
            checkTokenStatus();
            checkAuthentication();
        }

        function setTestToken() {
            // Create a test token that expires in 1 hour
            const payload = {
                user_id: 'test-user',
                email: '<EMAIL>',
                exp: Math.floor(Date.now() / 1000) + 3600,
                iat: Math.floor(Date.now() / 1000)
            };
            
            // Simple base64 encoding (not a real JWT signature)
            const header = btoa(JSON.stringify({typ: 'JWT', alg: 'HS256'}));
            const payloadStr = btoa(JSON.stringify(payload));
            const signature = 'test-signature';
            
            const testToken = `${header}.${payloadStr}.${signature}`;
            localStorage.setItem('access_token', testToken);
            
            console.log('🔧 Test token set');
            checkTokenStatus();
            checkAuthentication();
        }

        document.addEventListener('DOMContentLoaded', () => {
            checkTokenStatus();
            checkAuthentication();
        });
    </script>
</body>
</html>
