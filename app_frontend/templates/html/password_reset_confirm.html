{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置新密码 - 认知健康</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .confirm-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .confirm-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .confirm-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .confirm-header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .confirm-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
            background: white;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .confirm-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }

        .confirm-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .password-requirements {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 25px;
            font-size: 14px;
        }

        .password-requirements ul {
            margin: 10px 0 0 20px;
        }

        .password-requirements li {
            margin-bottom: 5px;
        }

        .back-links {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .back-links a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 500;
        }

        .back-links a:hover {
            text-decoration: underline;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .required {
            color: #e74c3c;
        }

        @media (max-width: 768px) {
            .confirm-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .confirm-header {
                padding: 30px 20px;
            }
            
            .confirm-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.location.href='/'">
        <i class="fas fa-arrow-left"></i> 返回首页
    </button>

    <div class="confirm-container">
        <div class="confirm-header">
            <h1><i class="fas fa-lock"></i> 设置新密码</h1>
            <p>请输入您的新密码</p>
        </div>

        <div class="confirm-form">
            <div class="password-requirements">
                <strong><i class="fas fa-shield-alt"></i> 密码要求：</strong>
                <ul>
                    <li>至少8个字符</li>
                    <li>包含大小写字母</li>
                    <li>包含数字</li>
                    <li>建议包含特殊字符</li>
                </ul>
            </div>

            <div id="confirmMessage"></div>

            <form id="passwordResetConfirmForm">
                <input type="hidden" id="token" name="token">
                
                <div class="form-group">
                    <label for="password">新密码 <span class="required">*</span></label>
                    <input type="password" id="password" name="password" required placeholder="请输入新密码">
                </div>

                <div class="form-group">
                    <label for="password_confirm">确认新密码 <span class="required">*</span></label>
                    <input type="password" id="password_confirm" name="password_confirm" required placeholder="请再次输入新密码">
                </div>

                <button type="submit" id="confirmBtn" class="confirm-btn">
                    <i class="fas fa-check"></i> 重置密码
                </button>
            </form>

            <div class="back-links">
                <a href="/login"><i class="fas fa-sign-in-alt"></i> 返回登录</a>
            </div>
        </div>
    </div>

    <script>
        // 从Django传递API配置到前端
        window.API_CONFIG = {
            API_BASE_URL: '{{ API_BASE_URL }}/api',
            LOCAL_BASE_URL: '{{ LOCAL_BASE_URL }}'
        };
    </script>
    <script src="{% static 'js/auth_api.js' %}"></script>
    <script>
        // 从URL参数中获取token
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            if (token) {
                document.getElementById('token').value = token;
            } else {
                document.getElementById('confirmMessage').innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        无效的重置链接，请重新申请密码重置。
                    </div>
                `;
                document.getElementById('passwordResetConfirmForm').style.display = 'none';
            }

            // 处理表单提交
            const form = document.getElementById('passwordResetConfirmForm');
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const token = document.getElementById('token').value;
                const password = document.getElementById('password').value;
                const passwordConfirm = document.getElementById('password_confirm').value;
                const submitBtn = document.getElementById('confirmBtn');
                const messageDiv = document.getElementById('confirmMessage');

                // 显示加载状态
                submitBtn.disabled = true;
                submitBtn.textContent = '重置中...';
                messageDiv.innerHTML = '';

                try {
                    const response = await authAPI.confirmPasswordReset(token, password, passwordConfirm);
                    
                    if (response.success) {
                        messageDiv.innerHTML = `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                ${response.message}
                            </div>
                        `;
                        form.reset();
                        
                        // 3秒后跳转到登录页面
                        setTimeout(() => {
                            window.location.href = '/login?message=密码重置成功，请使用新密码登录';
                        }, 3000);
                    } else {
                        let errorMessage = response.message || '重置失败';
                        if (response.errors) {
                            const errors = Object.values(response.errors).flat();
                            errorMessage = errors.join('<br>');
                        }
                        messageDiv.innerHTML = `
                            <div class="alert alert-error">
                                <i class="fas fa-exclamation-circle"></i>
                                ${errorMessage}
                            </div>
                        `;
                    }
                } catch (error) {
                    messageDiv.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle"></i>
                            网络错误，请重试。
                        </div>
                    `;
                } finally {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-check"></i> 重置密码';
                }
            });
        });
    </script>
</body>
</html>
