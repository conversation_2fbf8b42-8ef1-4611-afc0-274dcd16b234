{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>User Profile - MedVoice Pro</title>

    <!-- Performance Optimizations -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <!-- Font Awesome -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"></noscript>

    <!-- Critical CSS Inline -->
    <style>
        /* Modern CSS Reset */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* CSS Custom Properties */
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1e40af;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --surface-color: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header Section */
        .header {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: 3rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        }

        /* Profile Section */
        .profile-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .avatar-container {
            position: relative;
            flex-shrink: 0;
        }

        .avatar {
            width: 8rem;
            height: 8rem;
            border-radius: 50%;
            border: 4px solid var(--surface-color);
            box-shadow: var(--shadow-lg);
            object-fit: cover;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            font-weight: 600;
            transition: var(--transition);
        }

        .avatar:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-xl);
        }

        .avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-info {
            flex: 1;
        }

        .user-info h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }

        .user-info .email {
            color: var(--text-secondary);
            font-size: 1.125rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-info .joined {
            color: var(--text-muted);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        /* Statistics Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-color);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.25rem;
            color: white;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stat-card.total .stat-icon { background: var(--primary-color); }
        .stat-card.total .stat-number { color: var(--primary-color); }

        .stat-card.success .stat-icon { background: var(--success-color); }
        .stat-card.success .stat-number { color: var(--success-color); }

        .stat-card.warning .stat-icon { background: var(--warning-color); }
        .stat-card.warning .stat-number { color: var(--warning-color); }

        .stat-card.error .stat-icon { background: var(--error-color); }
        .stat-card.error .stat-number { color: var(--error-color); }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }

        .main-content {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .sidebar {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            padding: 2rem;
            height: fit-content;
        }

        .section-header {
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .history-list {
            padding: 0;
            max-height: 600px;
            overflow-y: auto;
        }

        .history-item {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
        }

        .history-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--primary-color);
            transform: scaleY(0);
            transition: var(--transition);
        }

        .history-item:hover {
            background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
            transform: translateX(4px);
            box-shadow: var(--shadow-lg);
            color: #1e293b !important;
            border-left: 4px solid var(--primary-color);
        }

        .history-item:hover::before {
            transform: scaleY(1);
        }

        .history-item:hover .history-title,
        .history-item:hover .history-filename,
        .history-item:hover .history-details,
        .history-item:hover .history-date {
            color: #1e293b !important;
        }

        .history-item:hover .history-details .detail-item {
            color: #475569 !important;
        }

        .history-item:hover .history-filename span {
            color: #1e293b !important;
        }

        .history-item:hover .history-status {
            background: rgba(255, 255, 255, 0.9) !important;
            color: inherit !important;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .history-date {
            color: var(--text-muted);
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .history-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .history-filename {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .history-status {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .history-details {
            display: flex;
            gap: 1rem;
            margin: 0.5rem 0;
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .status-completed {
            background: #dcfce7;
            color: #166534;
        }

        .status-processing {
            background: #fef3c7;
            color: #92400e;
        }

        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }

        /* Quick Actions */
        .quick-actions {
            margin-bottom: 2rem;
        }

        .quick-actions h3 {
            margin-bottom: 1.5rem;
            color: var(--text-primary);
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            width: 100%;
            padding: 1rem 1.5rem;
            margin-bottom: 0.75rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            text-decoration: none;
            border-radius: var(--radius-lg);
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
            text-decoration: none;
        }

        .action-btn.secondary {
            background: var(--background-color);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .action-btn.secondary:hover {
            background: #e2e8f0;
            color: var(--text-primary);
            box-shadow: var(--shadow-md);
        }

        /* Activity Summary */
        .activity-summary {
            background: var(--background-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .activity-value {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.875rem;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--text-muted);
        }

        .empty-state h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        .empty-state p {
            font-size: 0.875rem;
        }

        /* Navigation */
        .back-btn {
            position: fixed;
            top: 1.5rem;
            left: 1.5rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: 0.75rem 1.25rem;
            color: var(--primary-color);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        /* Loading States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-muted);
        }

        .loading i {
            margin-right: 0.75rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .container {
                padding: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .profile-section {
                flex-direction: column;
                text-align: center;
                gap: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .header {
                padding: 2rem;
            }

            .user-info h1 {
                font-size: 2rem;
            }

            .back-btn {
                top: 1rem;
                left: 1rem;
                padding: 0.5rem 1rem;
                font-size: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0.75rem;
            }

            .header {
                padding: 1.5rem;
            }

            .avatar {
                width: 6rem;
                height: 6rem;
                font-size: 2rem;
            }

            .user-info h1 {
                font-size: 1.75rem;
            }

            .stat-card {
                padding: 1.5rem;
            }

            .stat-number {
                font-size: 2rem;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --background-color: #0f172a;
                --surface-color: #1e293b;
                --text-primary: #f1f5f9;
                --text-secondary: #cbd5e1;
                --text-muted: #64748b;
                --border-color: #334155;
            }
        }
    </style>

    <!-- Application Configuration -->
    <script>
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";
    </script>
</head>
<body>
    <!-- Navigation -->
    <button class="back-btn" onclick="goBack()" aria-label="Go back">
        <i class="fas fa-arrow-left" aria-hidden="true"></i>
        <span>Back</span>
    </button>

    <div class="container">
        <!-- Profile Header -->
        <div class="header">
            <div class="profile-section">
                <div class="avatar-container">
                    <div id="profile-avatar" class="avatar" role="img" aria-label="User avatar">
                        <i class="fas fa-user" aria-hidden="true"></i>
                    </div>
                </div>

                <div class="user-info">
                    <h1 id="profile-username">Loading...</h1>
                    <div id="profile-email" class="email">
                        <i class="fas fa-envelope" aria-hidden="true"></i>
                        <span>Loading...</span>
                    </div>
                    <div id="profile-joined" class="joined">
                        <i class="fas fa-calendar-alt" aria-hidden="true"></i>
                        <span>Loading...</span>
                    </div>
                    <div id="profile-badge" class="user-badge" style="display: none;">
                        <i class="fas fa-crown" aria-hidden="true"></i>
                        <span>Premium User</span>
                    </div>
                </div>
            </div>

            <!-- Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card total">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line" aria-hidden="true"></i>
                    </div>
                    <div id="total-analyses" class="stat-number">0</div>
                    <div class="stat-label">Total Analyses</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle" aria-hidden="true"></i>
                    </div>
                    <div id="successful-analyses" class="stat-number">0</div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-clock" aria-hidden="true"></i>
                    </div>
                    <div id="pending-analyses" class="stat-number">0</div>
                    <div class="stat-label">Processing</div>
                </div>
                <div class="stat-card error">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                    </div>
                    <div id="failed-analyses" class="stat-number">0</div>
                    <div class="stat-label">Failed</div>
                </div>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <!-- Main Content - Recent Analysis History -->
            <div class="main-content">
                <div class="section-header">
                    <i class="fas fa-history" aria-hidden="true"></i>
                    <span>Recent Analysis History</span>
                </div>
                <div id="recent-history" class="history-list">
                    <div class="loading">
                        <i class="fas fa-spinner" aria-hidden="true"></i>
                        <span>Loading history...</span>
                    </div>
                </div>
            </div>

            <!-- Sidebar - Quick Actions -->
            <div class="sidebar">
                <div class="quick-actions">
                    <h3>
                        <i class="fas fa-bolt" aria-hidden="true"></i>
                        <span>Quick Actions</span>
                    </h3>
                    <a href="/audio_upload/" class="action-btn" aria-label="Start new audio analysis">
                        <i class="fas fa-microphone" aria-hidden="true"></i>
                        <span>New Analysis</span>
                    </a>
                    <a href="/audio_upload/history/" class="action-btn secondary" aria-label="View complete analysis history">
                        <i class="fas fa-list" aria-hidden="true"></i>
                        <span>Full History</span>
                    </a>
                    <a href="/user/settings/" class="action-btn secondary" aria-label="Account settings">
                        <i class="fas fa-cog" aria-hidden="true"></i>
                        <span>Settings</span>
                    </a>
                </div>

                <!-- User Activity Summary -->
                <div class="quick-actions">
                    <h3>
                        <i class="fas fa-chart-pie" aria-hidden="true"></i>
                        <span>Activity Summary</span>
                    </h3>
                    <div class="activity-summary">
                        <div class="activity-item">
                            <span class="activity-label">This Week</span>
                            <span id="week-analyses" class="activity-value">0</span>
                        </div>
                        <div class="activity-item">
                            <span class="activity-label">This Month</span>
                            <span id="month-analyses" class="activity-value">0</span>
                        </div>
                        <div class="activity-item">
                            <span class="activity-label">Average Score</span>
                            <span id="avg-score" class="activity-value">N/A</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        /**
         * Modern Profile Application
         * High-performance user profile management
         */
        class ProfileApp {
            constructor() {
                this.config = {
                    apiBaseUrl: window.API_BASE_URL,
                    localBaseUrl: window.LOCAL_BASE_URL
                };

                this.cache = new Map();
                this.isLoading = false;

                this.init();
            }

            async init() {
                console.log('Initializing ProfileApp...');
                console.log('Config:', this.config);

                try {
                    // Load user profile first
                    await this.loadUserProfile();

                    // Then load other data
                    await this.loadRecentHistory();
                    await this.loadActivitySummary();
                } catch (error) {
                    console.error('Failed to initialize profile:', error);
                    this.showError('Failed to load profile data');
                }
            }

            // Authentication helpers (using JWT)
            getAuthToken() {
                return localStorage.getItem('access_token');
            }

            isTokenExpired(token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    return payload.exp < Date.now() / 1000;
                } catch {
                    return true;
                }
            }

            async makeAuthenticatedRequest(url, options = {}) {
                const token = this.getAuthToken();

                if (!token || this.isTokenExpired(token)) {
                    window.location.href = '/login/';
                    return null;
                }

                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                if (response.status === 401) {
                    localStorage.removeItem('access_token');
                    window.location.href = '/login/';
                    return null;
                }

                return response;
            }

            // Load user profile data
            async loadUserProfile() {
                try {
                    const url = `${this.config.apiBaseUrl}/api/user/profile/`;
                    console.log('Loading profile from:', url);

                    const response = await this.makeAuthenticatedRequest(url);

                    if (!response) {
                        console.log('No response received');
                        return;
                    }

                    console.log('Response status:', response.status);

                    if (response.ok) {
                        const data = await response.json();
                        console.log('Profile data received:', data);
                        this.updateProfileDisplay(data);
                        this.cache.set('profile', data);
                    } else {
                        const errorText = await response.text();
                        console.error('Response error:', errorText);

                        // Use fallback data for testing
                        console.warn('Using fallback user data');
                        const fallbackData = {
                            name: 'Current User',
                            email: '<EMAIL>',
                            date_joined: new Date().toISOString(),
                            avatar_url: null
                        };
                        this.updateProfileDisplay(fallbackData);
                    }
                } catch (error) {
                    console.error('Failed to load profile:', error);
                    this.showError('Failed to load profile information');
                }
            }

            // Update profile display
            updateProfileDisplay(data) {
                console.log('Profile data received:', data);

                // Handle backend API response structure
                const user = data.data || data.user || data;
                const stats = data.stats || null;

                // Update basic information - use display_name or full_name from backend
                const displayName = user.display_name || user.full_name ||
                                  `${user.first_name || ''} ${user.last_name || ''}`.trim() ||
                                  (user.email ? user.email.split('@')[0] : 'User');

                document.getElementById('profile-username').textContent = displayName;

                const emailElement = document.getElementById('profile-email');
                const emailSpan = emailElement.querySelector('span');
                if (emailSpan) {
                    emailSpan.textContent = user.email || 'No email';
                } else {
                    emailElement.innerHTML = `<i class="fas fa-envelope" aria-hidden="true"></i><span>${user.email || 'No email'}</span>`;
                }

                // Format join date
                if (user.date_joined) {
                    const joinDate = new Date(user.date_joined).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                    const joinedElement = document.getElementById('profile-joined');
                    const joinedSpan = joinedElement.querySelector('span');
                    if (joinedSpan) {
                        joinedSpan.textContent = `Joined ${joinDate}`;
                    } else {
                        joinedElement.innerHTML = `<i class="fas fa-calendar-alt" aria-hidden="true"></i><span>Joined ${joinDate}</span>`;
                    }
                }

                // Update avatar
                this.updateAvatar(user);

                // Update statistics from backend data
                if (stats) {
                    this.updateStatistics(stats);
                } else {
                    // If no stats in profile response, load them separately
                    this.loadUserStatistics();
                }

                // Show premium badge if applicable
                if (user.is_premium) {
                    const badge = document.getElementById('profile-badge');
                    if (badge) {
                        badge.style.display = 'flex';
                    }
                }
            }

            // Update user avatar
            updateAvatar(user) {
                const avatar = document.getElementById('profile-avatar');

                // Check for avatar field from backend (ImageField)
                if (user.avatar && user.avatar !== null) {
                    // If avatar is a full URL, use it directly; otherwise prepend API base URL
                    const avatarUrl = user.avatar.startsWith('http') ?
                                    user.avatar :
                                    `${this.config.apiBaseUrl}${user.avatar}`;
                    avatar.innerHTML = `<img src="${avatarUrl}" alt="User avatar" loading="lazy">`;
                } else {
                    // Use initials based on first_name and last_name
                    const initials = this.getInitials(user.first_name, user.last_name, user.email);
                    avatar.innerHTML = initials;
                }
            }

            // Get user initials
            getInitials(firstName, lastName, email) {
                if (firstName && lastName) {
                    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
                } else if (firstName) {
                    return firstName.charAt(0).toUpperCase();
                } else if (lastName) {
                    return lastName.charAt(0).toUpperCase();
                } else if (email) {
                    return email.charAt(0).toUpperCase();
                } else {
                    return 'U';
                }
            }

            // Load user statistics from audio history
            async loadUserStatistics() {
                try {
                    const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/audio_history/`
                    );

                    if (!response) return;

                    if (response.ok) {
                        const data = await response.json();
                        const historyItems = data.data || data.results || [];

                        // Calculate statistics from history data
                        const stats = this.calculateStatistics(historyItems);
                        this.updateStatistics(stats);
                    } else {
                        console.error('Failed to load statistics');
                        // Use default stats
                        this.updateStatistics({
                            total: 0,
                            completed: 0,
                            processing: 0,
                            failed: 0
                        });
                    }
                } catch (error) {
                    console.error('Failed to load statistics:', error);
                    this.updateStatistics({
                        total: 0,
                        completed: 0,
                        processing: 0,
                        failed: 0
                    });
                }
            }

            // Calculate statistics from history items
            calculateStatistics(historyItems) {
                const total = historyItems.length;
                const completed = historyItems.filter(item => item.status === 'completed').length;
                const processing = historyItems.filter(item => item.status === 'processing').length;
                const failed = historyItems.filter(item => item.status === 'failed').length;

                return {
                    total,
                    completed,
                    processing,
                    failed
                };
            }

            // Update statistics display
            updateStatistics(stats) {
                console.log('Updating statistics:', stats);

                const elements = {
                    'total-analyses': stats.total || 0,
                    'successful-analyses': stats.completed || stats.successful || 0,
                    'pending-analyses': stats.processing || stats.pending || 0,
                    'failed-analyses': stats.failed || 0
                };

                Object.entries(elements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        this.animateNumber(element, value);
                    }
                });
            }

            // Animate number changes
            animateNumber(element, targetValue) {
                const startValue = parseInt(element.textContent) || 0;
                const duration = 1000;
                const startTime = performance.now();

                const animate = (currentTime) => {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    const currentValue = Math.round(startValue + (targetValue - startValue) * progress);
                    element.textContent = currentValue;

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    }
                };

                requestAnimationFrame(animate);
            }

            // Load recent analysis history
            async loadRecentHistory() {
                try {
                    const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/audio_history/`
                    );

                    if (!response) return;

                    if (response.ok) {
                        const data = await response.json();
                        console.log('History data received:', data);

                        // Handle backend response structure
                        const historyItems = data.data || data.results || [];

                        // Take only first 5 items for recent history
                        const recentItems = historyItems.slice(0, 5);

                        this.displayRecentHistory(recentItems);
                        this.cache.set('recentHistory', historyItems);

                        // Also calculate and update statistics if not already done
                        if (!this.cache.get('statsLoaded')) {
                            const stats = this.calculateStatistics(historyItems);
                            this.updateStatistics(stats);
                            this.cache.set('statsLoaded', true);
                        }
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    console.error('Failed to load history:', error);
                    // Show empty state instead of error for better UX
                    this.displayRecentHistory([]);
                }
            }

            // Display recent analysis history
            displayRecentHistory(analyses) {
                const container = document.getElementById('recent-history');

                if (!analyses || analyses.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-microphone-slash" aria-hidden="true"></i>
                            <h3>No Analysis Records</h3>
                            <p>Start your first audio analysis!</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = analyses.map(analysis => `
                    <div class="history-item" onclick="profileApp.viewAnalysisDetail('${analysis.id}')" role="button" tabindex="0" aria-label="View analysis details">
                        <div class="history-header">
                            <div class="history-date">${this.formatDate(analysis.upload_time)}</div>
                        </div>
                        <div class="history-filename">
                            <i class="fas fa-file-audio" aria-hidden="true"></i>
                            <span>${this.escapeHtml(analysis.filename || 'Audio Analysis')}</span>
                        </div>
                        <div class="history-title">Predicted MMSE Score: ${this.parseMMSEScore(analysis.result)}</div>
                        <div class="history-details">
                            <span class="detail-item">Spoken by: ${this.formatRelationship(analysis.relationship)}</span>
                            <span class="detail-item">Age: ${analysis.age || 'N/A'}</span>
                        </div>
                        <span class="history-status ${this.getStatusClass(analysis.status)}">${this.getStatusText(analysis.status)}</span>
                    </div>
                `).join('');

                // Add keyboard navigation
                container.querySelectorAll('.history-item').forEach(item => {
                    item.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            item.click();
                        }
                    });
                });
            }

            // Load activity summary
            async loadActivitySummary() {
                try {
                    // For now, calculate from history since the endpoint might not exist
                    this.calculateActivityFromHistory();
                } catch (error) {
                    console.error('Failed to load activity summary:', error);
                    this.calculateActivityFromHistory();
                }
            }

            // Update activity summary display
            updateActivitySummary(data) {
                const elements = {
                    'week-analyses': data.week_count || 0,
                    'month-analyses': data.month_count || 0,
                    'avg-score': data.average_score ? data.average_score.toFixed(1) : 'N/A'
                };

                Object.entries(elements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                });
            }

            // Calculate activity from cached history if API endpoint doesn't exist
            calculateActivityFromHistory() {
                const history = this.cache.get('recentHistory') || [];
                const now = new Date();
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

                const weekCount = history.filter(item =>
                    new Date(item.upload_time) >= weekAgo
                ).length;

                const monthCount = history.filter(item =>
                    new Date(item.upload_time) >= monthAgo
                ).length;

                // Calculate average MMSE score from completed analyses only
                const completedAnalyses = history.filter(item => item.status === 'completed');
                const scores = completedAnalyses
                    .map(item => {
                        const score = this.parseMMSEScore(item.result);
                        return score !== 'N/A' ? parseFloat(score) : null;
                    })
                    .filter(score => score !== null && !isNaN(score));

                const avgScore = scores.length > 0
                    ? (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1)
                    : 'N/A';

                this.updateActivitySummary({
                    week_count: weekCount,
                    month_count: monthCount,
                    average_score: avgScore === 'N/A' ? null : parseFloat(avgScore)
                });
            }

            // Utility methods
            formatDate(dateString) {
                if (!dateString) return 'Unknown';
                try {
                    return new Date(dateString).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch {
                    return 'Invalid date';
                }
            }

            getStatusClass(status) {
                switch (status?.toLowerCase()) {
                    case 'completed': return 'status-completed';
                    case 'processing': return 'status-processing';
                    case 'failed': return 'status-failed';
                    default: return 'status-processing';
                }
            }

            getStatusText(status) {
                switch (status?.toLowerCase()) {
                    case 'completed': return 'Completed';
                    case 'processing': return 'Processing';
                    case 'failed': return 'Failed';
                    default: return 'Unknown';
                }
            }

            parseMMSEScore(result) {
                try {
                    if (!result) return 'N/A';
                    const parsed = typeof result === 'string' ? JSON.parse(result) : result;
                    const score = parsed['Predicted mmse score'];
                    return typeof score === 'number' ? score.toFixed(1) : (parseFloat(score) || 0).toFixed(1);
                } catch {
                    return 'N/A';
                }
            }

            formatRelationship(relationship) {
                const relationshipMap = {
                    'my_self': 'Myself',
                    'my_father': 'My Father',
                    'my_mother': 'My Mother',
                    'my_father_in_law': 'My Father in law',
                    'my_mother_in_law': 'My Mother in law',
                    'my_grandfather': 'My Grandfather',
                    'my_grandmother': 'My Grandmother',
                    'great_grandfather': 'My Great-Grandfather',
                    'great_grandmother': 'My Great-Grandmother',
                    'my_friend': 'My Friend',
                    'others': 'Other'
                };
                return relationshipMap[relationship] || relationship || 'Not specified';
            }

            escapeHtml(text) {
                if (!text) return '';
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // Navigation methods
            viewAnalysisDetail(analysisId) {
                try {
                    // Find the analysis item from cached data
                    const recentHistory = this.cache.get('recentHistory') || [];
                    const analysisItem = recentHistory.find(item => item.id == analysisId);

                    if (analysisItem) {
                        console.log('Found analysis item in cache:', analysisItem);
                        // Store complete analysis details for details page
                        sessionStorage.setItem('analysisDetails', JSON.stringify(analysisItem));
                        window.location.href = '/audio_upload/details/';
                    } else {
                        console.log('Analysis item not found in cache, fetching all history...');
                        // If not found in recent history, fetch all history and try again
                        this.fetchAllHistoryAndViewDetail(analysisId);
                    }
                } catch (error) {
                    console.error('Failed to navigate to details:', error);
                    alert('Failed to load analysis details. Please try again.');
                }
            }

            // Fetch all history and find the specific analysis
            async fetchAllHistoryAndViewDetail(analysisId) {
                try {
                    const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/audio_history/`
                    );

                    if (response && response.ok) {
                        const data = await response.json();
                        const allHistory = data.data || data.results || [];

                        // Find the specific analysis item
                        const analysisItem = allHistory.find(item => item.id == analysisId);

                        if (analysisItem) {
                            console.log('Found analysis item in full history:', analysisItem);
                            // Store complete analysis details for details page
                            sessionStorage.setItem('analysisDetails', JSON.stringify(analysisItem));
                            window.location.href = '/audio_upload/details/';
                        } else {
                            console.error('Analysis item not found in history');
                            alert('Analysis record not found. It may have been deleted.');
                        }
                    } else {
                        console.error('Failed to fetch history');
                        alert('Failed to load analysis history. Please try again.');
                    }
                } catch (error) {
                    console.error('Failed to fetch history:', error);
                    alert('Failed to load analysis details. Please try again.');
                }
            }

            // Error handling
            showError(message) {
                const container = document.getElementById('recent-history');
                if (container) {
                    container.innerHTML = `
                        <div class="empty-state error">
                            <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                            <h3>Error</h3>
                            <p>${this.escapeHtml(message)}</p>
                        </div>
                    `;
                }
            }

            showHistoryError() {
                const container = document.getElementById('recent-history');
                if (container) {
                    container.innerHTML = `
                        <div class="empty-state error">
                            <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                            <h3>Failed to Load History</h3>
                            <p>Unable to load recent analysis history.</p>
                        </div>
                    `;
                }
            }
        }

        // Global navigation function
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/audio_upload/';
            }
        }

        // Initialize application
        let profileApp;
        document.addEventListener('DOMContentLoaded', () => {
            try {
                profileApp = new ProfileApp();
                window.profileApp = profileApp; // For debugging
            } catch (error) {
                console.error('Failed to initialize profile app:', error);
            }
        });
    </script>
</body>
</html>
