#!/usr/bin/env python
"""
API测试脚本
用于测试用户认证相关的API接口
"""

import requests
import json
import time

# API配置
API_BASE_URL = "http://192.168.50.180:8000/api"  # Fixed: Backend API runs on port 8000
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpassword123"


class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.access_token = None
        self.refresh_token = None
    
    def print_response(self, response, title):
        """打印响应信息"""
        print(f"\n{'='*50}")
        print(f"🧪 {title}")
        print(f"{'='*50}")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        try:
            data = response.json()
            print(f"响应体: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except:
            print(f"响应体: {response.text}")
    
    def test_health_check(self):
        """测试健康检查"""
        response = self.session.get(f"{API_BASE_URL}/health/")
        self.print_response(response, "健康检查")
        return response.status_code == 200
    
    def test_register(self):
        """测试用户注册"""
        data = {
            "email": TEST_EMAIL,
            "password": TEST_PASSWORD,
            "password_confirm": TEST_PASSWORD,
            "first_name": "测试",
            "last_name": "用户"
        }
        response = self.session.post(f"{API_BASE_URL}/register/", json=data)
        self.print_response(response, "用户注册")
        return response.status_code == 201
    
    def test_check_email(self):
        """测试邮箱检查"""
        response = self.session.get(f"{API_BASE_URL}/check-email/?email={TEST_EMAIL}")
        self.print_response(response, "邮箱检查")
        return response.status_code == 200
    
    def test_login_before_activation(self):
        """测试激活前登录"""
        data = {
            "email": TEST_EMAIL,
            "password": TEST_PASSWORD
        }
        response = self.session.post(f"{API_BASE_URL}/login/", json=data)
        self.print_response(response, "激活前登录")
        return response.status_code == 400
    
    def test_resend_verification(self):
        """测试重新发送验证邮件"""
        data = {"email": TEST_EMAIL}
        response = self.session.post(f"{API_BASE_URL}/resend-verification/", json=data)
        self.print_response(response, "重新发送验证邮件")
        return response.status_code == 200
    
    def test_password_reset_request(self):
        """测试密码重置请求"""
        data = {"email": TEST_EMAIL}
        response = self.session.post(f"{API_BASE_URL}/password-reset/", json=data)
        self.print_response(response, "密码重置请求")
        return response.status_code == 200
    
    def test_login_with_token(self):
        """测试使用token登录"""
        if not self.access_token:
            print("❌ 没有访问令牌，跳过测试")
            return False
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = self.session.get(f"{API_BASE_URL}/profile/", headers=headers)
        self.print_response(response, "获取用户资料")
        return response.status_code == 200
    
    def test_token_refresh(self):
        """测试令牌刷新"""
        if not self.refresh_token:
            print("❌ 没有刷新令牌，跳过测试")
            return False
        
        data = {"refresh": self.refresh_token}
        response = self.session.post(f"{API_BASE_URL}/token/refresh/", json=data)
        self.print_response(response, "令牌刷新")
        
        if response.status_code == 200:
            data = response.json()
            self.access_token = data.get("access")
            return True
        return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧠 认知健康 API 测试")
        print("=" * 60)
        
        tests = [
            ("健康检查", self.test_health_check),
            ("用户注册", self.test_register),
            ("邮箱检查", self.test_check_email),
            ("激活前登录", self.test_login_before_activation),
            ("重新发送验证邮件", self.test_resend_verification),
            ("密码重置请求", self.test_password_reset_request),
            ("令牌刷新", self.test_token_refresh),
            ("获取用户资料", self.test_login_with_token),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, "✅ 通过" if result else "❌ 失败"))
            except Exception as e:
                results.append((test_name, f"❌ 异常: {e}"))
            
            time.sleep(1)  # 避免请求过快
        
        # 打印测试结果
        print(f"\n{'='*60}")
        print("📊 测试结果汇总")
        print(f"{'='*60}")
        for test_name, result in results:
            print(f"{test_name:<20} {result}")
        
        passed = sum(1 for _, result in results if result.startswith("✅"))
        total = len(results)
        print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")


def main():
    """主函数"""
    tester = APITester()
    tester.run_all_tests()
    
    print(f"\n{'='*60}")
    print("📝 测试说明")
    print(f"{'='*60}")
    print("1. 邮箱激活功能需要手动测试（检查邮件）")
    print("2. 密码重置功能需要手动测试（检查邮件）")
    print("3. 完整的登录流程需要先激活账户")
    print("4. 如需测试完整流程，请先激活测试账户")
    print(f"\n测试邮箱: {TEST_EMAIL}")
    print(f"测试密码: {TEST_PASSWORD}")


if __name__ == "__main__":
    main()
