#!/usr/bin/env python3
"""
测试联系信息添加
"""

import os
import sys
import django
from datetime import datetime

# 设置Django环境
try:
    sys.path.append('.')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
    django.setup()
    from audio_upload.views import generate_pdf_with_xhtml2pdf
    DJANGO_AVAILABLE = True
    print(f"✅ Django环境可用")
except Exception as e:
    DJANGO_AVAILABLE = False
    print(f"⚠️  Django环境不可用: {e}")

def test_contact_info_in_html():
    """测试audio_details.html中的联系信息"""
    print("\n🔍 检查audio_details.html中的联系信息...")
    
    try:
        with open('audio_upload/templates/audio_details.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含联系信息
        if 'CONTACT INFORMATION:' in content and '<EMAIL>' in content:
            print("✅ audio_details.html中已包含联系信息")
            
            # 检查CSS样式
            if '.contact-info' in content:
                print("✅ audio_details.html中已包含.contact-info CSS样式")
            else:
                print("❌ audio_details.html中缺少.contact-info CSS样式")
            
            return True
        else:
            print("❌ audio_details.html中未找到联系信息")
            return False
            
    except Exception as e:
        print(f"❌ 检查audio_details.html失败: {e}")
        return False

def test_contact_info_in_pdf():
    """测试PDF中的联系信息"""
    if not DJANGO_AVAILABLE:
        print("⚠️  Django环境不可用，跳过PDF测试")
        return False
    
    print("\n🔍 测试PDF中的联系信息...")
    
    # 测试数据
    test_data = {
        'filename': 'contact_info_test.wav',
        'age': '65',
        'relationship': 'my_self',
        'occupation': 'retired'
    }
    
    test_result = {
        'Predicted mmse score': 25.3,
        'Percentile': 79,
        'Model': 'Contact Info Test Model'
    }
    
    try:
        # 生成PDF
        pdf_bytes = generate_pdf_with_xhtml2pdf(test_data, test_result)
        
        # 保存PDF文件
        output_filename = 'test_contact_info.pdf'
        with open(output_filename, 'wb') as f:
            f.write(pdf_bytes)
        
        print(f"✅ 包含联系信息的PDF生成成功!")
        print(f"📄 文件大小: {len(pdf_bytes)} bytes")
        print(f"📁 文件保存为: {output_filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_pdf_source_code():
    """检查PDF源代码中的联系信息"""
    print("\n🔍 检查PDF源代码中的联系信息...")
    
    try:
        with open('audio_upload/views.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含联系信息
        if 'CONTACT INFORMATION:' in content and '<EMAIL>' in content:
            print("✅ PDF源代码中已包含联系信息")
            
            # 检查CSS样式
            if '.contact-info' in content:
                print("✅ PDF源代码中已包含.contact-info CSS样式")
            else:
                print("❌ PDF源代码中缺少.contact-info CSS样式")
            
            return True
        else:
            print("❌ PDF源代码中未找到联系信息")
            return False
            
    except Exception as e:
        print(f"❌ 检查PDF源代码失败: {e}")
        return False

def create_contact_info_demo():
    """创建联系信息演示"""
    print("\n🔄 创建联系信息演示...")
    
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Contact Information Demo</title>
        <style>
            body {{
                font-family: 'Times New Roman', Times, serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 2rem;
                background-color: #f5f5f5;
            }}
            .demo-container {{
                background: white;
                padding: 2rem;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .section {{
                margin-bottom: 2rem;
                padding: 1.5rem;
                border: 1px solid #ddd;
                border-radius: 5px;
            }}
            .section h3 {{
                margin-top: 0;
                color: #333;
                border-bottom: 2px solid #2196F3;
                padding-bottom: 0.5rem;
            }}
            .disclaimer {{
                line-height: 1.6;
                margin-bottom: 1rem;
                background: #f9f9f9;
                padding: 1rem;
                border-left: 4px solid #ff9800;
            }}
            .contact-info {{
                line-height: 1.6;
                margin-bottom: 1rem;
                margin-top: 1rem;
                background: #e3f2fd;
                padding: 1rem;
                border-left: 4px solid #2196F3;
            }}
            .success {{
                background: #e8f5e8;
                border: 1px solid #4caf50;
                color: #2e7d32;
                padding: 1rem;
                border-radius: 5px;
                margin: 1rem 0;
            }}
        </style>
    </head>
    <body>
        <div class="demo-container">
            <h1>联系信息添加演示</h1>
            
            <div class="success">
                <h3>✅ 添加完成</h3>
                <p>联系信息已成功添加到audio_details.html和PDF生成代码中。</p>
            </div>
            
            <div class="section">
                <h3>在audio_details.html中的显示效果</h3>
                
                <div class="disclaimer">
                    <strong>MEDICAL DISCLAIMER:</strong> This computational analysis report is generated using artificial intelligence algorithms for research and clinical decision support purposes. The acoustic and linguistic biomarkers presented herein are derived from automated speech analysis and should be interpreted within the context of comprehensive clinical assessment. This report does not constitute a medical diagnosis, clinical recommendation, or therapeutic intervention.
                </div>
                
                <div class="contact-info">
                    <strong>CONTACT INFORMATION:</strong> For any technical support and collaboration, <NAME_EMAIL>.
                </div>
            </div>
            
            <div class="section">
                <h3>在PDF中的显示效果</h3>
                <p>PDF中也会显示相同的联系信息，位置在MEDICAL DISCLAIMER之后，文件名信息之前。</p>
                
                <div style="border: 1px solid #ccc; padding: 1rem; background: #fafafa;">
                    <div class="disclaimer">
                        <strong>MEDICAL DISCLAIMER:</strong> [完整的医疗免责声明文本]
                    </div>
                    
                    <div class="contact-info">
                        <strong>CONTACT INFORMATION:</strong> For any technical support and collaboration, <NAME_EMAIL>.
                    </div>
                    
                    <p style="margin: 1rem 0 0 0; font-size: 0.9em; color: #666;">
                        Report for File Name: example.wav
                    </p>
                </div>
            </div>
            
            <div class="section">
                <h3>技术实现</h3>
                <ul>
                    <li><strong>HTML结构:</strong> 添加了&lt;p class="contact-info"&gt;元素</li>
                    <li><strong>CSS样式:</strong> 添加了.contact-info样式类</li>
                    <li><strong>位置:</strong> MEDICAL DISCLAIMER之后</li>
                    <li><strong>一致性:</strong> audio_details.html和PDF中完全一致</li>
                </ul>
            </div>
            
            <div class="section">
                <h3>联系信息详情</h3>
                <p><strong>邮箱:</strong> <EMAIL></p>
                <p><strong>用途:</strong> 技术支持和合作</p>
                <p><strong>显示位置:</strong> 页面底部，医疗免责声明之后</p>
            </div>
            
            <div class="success">
                <p><strong>测试时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>状态:</strong> ✅ 联系信息已成功添加到两个位置</p>
            </div>
        </div>
    </body>
    </html>"""
    
    # 保存演示文件
    with open('contact_info_demo.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 联系信息演示文件已创建: contact_info_demo.html")
    return True

if __name__ == '__main__':
    print("🔍 开始测试联系信息添加...")
    
    # 测试1: 检查HTML文件
    success1 = test_contact_info_in_html()
    
    # 测试2: 检查PDF源代码
    success2 = check_pdf_source_code()
    
    # 测试3: 生成包含联系信息的PDF
    success3 = test_contact_info_in_pdf()
    
    # 测试4: 创建演示
    success4 = create_contact_info_demo()
    
    if success1 and success2:
        print("\n🎉 联系信息添加完成!")
        print("\n📋 添加内容:")
        print("✅ CONTACT INFORMATION: For any technical support and collaboration, <NAME_EMAIL>.")
        print("\n📍 添加位置:")
        print("✅ audio_details.html: MEDICAL DISCLAIMER之后")
        print("✅ PDF生成代码: MEDICAL DISCLAIMER之后，文件名之前")
        print("\n🎨 样式:")
        print("✅ 添加了.contact-info CSS类")
        print("✅ 与.disclaimer样式保持一致")
        print("\n💡 现在用户可以在详情页和PDF底部看到联系信息！")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
