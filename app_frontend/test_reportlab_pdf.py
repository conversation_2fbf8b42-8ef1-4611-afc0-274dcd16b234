#!/usr/bin/env python3
"""
测试ReportLab PDF生成功能
"""

import json
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from io import BytesIO

# 模拟测试数据
test_data = {
    "filename": "test_audio.wav",
    "age": "65",
    "relationship": "my_self",
    "occupation": "retired",
    "uploaded_at": "2025-01-17T10:30:00Z",
    "result": json.dumps({
        "Predicted mmse score": 24.5,
        "Percentile": 75,
        "Model": "Advanced Neural Network",
        "Model performance": {
            "RMSE": 2.34,
            "Pearson correlation coefficient": 0.87
        },
        "Transcribed": "The quick brown fox jumps over the lazy dog. This is a test transcription for PDF generation using ReportLab.",
        "Transcribed with color": '<span class="word-normal">The quick brown fox</span> <span class="word-hesitation">jumps</span> over the lazy dog.',
        "Selected features": {
            "prosodic": [
                {
                    "feature name": "speaking_rate",
                    "value": 0.00123,
                    "clsi lower": 0.001,
                    "clsi upper": 0.002,
                    "brief introduction": "Rate of speech measured in syllables per second"
                },
                {
                    "feature name": "pause_duration",
                    "value": 1234,
                    "clsi lower": 1000,
                    "clsi upper": 2000,
                    "brief introduction": "Average duration of pauses in milliseconds"
                }
            ],
            "linguistic": [
                {
                    "feature name": "honore_statistic",
                    "value": 0.000456,
                    "clsi lower": 0.0003,
                    "clsi upper": 0.0006,
                    "brief introduction": "Measure of lexical diversity in speech"
                }
            ]
        }
    })
}

def format_number(value):
    """标准的数值格式化函数"""
    if value is None:
        return 'Not provided'
    
    try:
        num = float(value)
        
        # 处理零值
        if num == 0:
            return '0'
        
        # 处理整数
        if num == int(num) and abs(num) < 1000:
            return str(int(num))
        
        abs_num = abs(num)
        
        # 判断是否需要科学记数法：过大(≥1000)或过小(<0.01)的数值
        if abs_num >= 1000 or (abs_num < 0.01 and abs_num != 0):
            # 使用标准的科学记数法格式
            scientific_str = f"{num:.2e}"  # 保留2位小数，总共3位有效数字
            mantissa, exponent_str = scientific_str.split('e')
            exponent = int(exponent_str)
            
            # 格式化为标准的10为底科学记数法
            return f"{mantissa} × 10^{exponent}"
        
        # 对于正常范围的数值，保留3位有效数字
        return f"{float(f'{num:.3g}'):g}"
    except (ValueError, TypeError):
        return str(value)

def generate_test_pdf():
    """生成测试PDF"""
    try:
        # 解析测试数据
        analysis_result = json.loads(test_data['result'])
        
        # 创建PDF缓冲区
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

        # 获取样式
        styles = getSampleStyleSheet()
        
        # 自定义样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            fontName='Times-Bold'
        )
        
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=20,
            alignment=TA_CENTER,
            fontName='Times-Bold'
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            fontName='Times-Bold'
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=11,
            fontName='Times-Roman'
        )

        # 构建PDF内容
        story = []

        # 标题
        story.append(Paragraph("Cognitive Health Speech Analysis", title_style))
        story.append(Paragraph("CONFIDENTIAL SCIENTIFIC REPORT", subtitle_style))
        story.append(Spacer(1, 20))

        # 患者信息表格
        demographics_data = [
            ['Audio File Name:', test_data['filename'], 'Date of Report:', datetime.now().strftime('%d-%m-%Y')],
            ['This Audio is Spoken by:', 'Myself', 'Occupation:', 'Retired'],
            ['Age:', test_data['age'], '', '']
        ]
        
        demographics_table = Table(demographics_data, colWidths=[2*inch, 2*inch, 1.5*inch, 1.5*inch])
        demographics_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Times-Roman'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(demographics_table)
        story.append(Spacer(1, 30))

        # 预测结果
        story.append(Paragraph("Prediction Results", heading_style))
        
        prediction_data = [
            ['Predicted MMSE Score (from voice)', 'Percentile'],
            [format_number(analysis_result.get('Predicted mmse score')), f"{analysis_result.get('Percentile', 'Not provided')}%"]
        ]
        
        prediction_table = Table(prediction_data, colWidths=[3.5*inch, 3.5*inch])
        prediction_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Times-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Times-Roman'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(prediction_table)
        story.append(Spacer(1, 30))

        # 科学记数法测试
        story.append(Paragraph("Scientific Notation Test", heading_style))
        
        test_values = [
            ['Original Value', 'Formatted Result', 'Type'],
            ['0.00123', format_number(0.00123), 'Small number'],
            ['1234', format_number(1234), 'Large number'],
            ['24.5', format_number(24.5), 'Normal number'],
            ['0.000456', format_number(0.000456), 'Very small number']
        ]
        
        test_table = Table(test_values, colWidths=[2*inch, 2.5*inch, 2.5*inch])
        test_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Times-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Times-Roman'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(test_table)
        story.append(Spacer(1, 30))

        # 免责声明
        disclaimer_text = "MEDICAL DISCLAIMER: This is a test report generated for demonstration purposes using ReportLab."
        disclaimer_style = ParagraphStyle(
            'Disclaimer',
            parent=normal_style,
            fontSize=9,
            fontName='Times-Roman',
            leftIndent=20,
            rightIndent=20
        )
        
        story.append(Paragraph(disclaimer_text, disclaimer_style))
        story.append(Spacer(1, 10))
        story.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", normal_style))

        # 生成PDF
        doc.build(story)
        
        # 返回PDF字节
        pdf_bytes = buffer.getvalue()
        buffer.close()
        
        return pdf_bytes

    except Exception as e:
        print(f"PDF生成失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    print("正在使用ReportLab生成PDF测试文件...")
    
    pdf_bytes = generate_test_pdf()
    
    if pdf_bytes:
        # 保存PDF文件
        with open('test_reportlab_output.pdf', 'wb') as f:
            f.write(pdf_bytes)
        
        print("✅ PDF生成成功!")
        print(f"📄 文件大小: {len(pdf_bytes)} bytes")
        print("📁 文件保存为: test_reportlab_output.pdf")
        
        # 验证文件是否存在
        import os
        if os.path.exists('test_reportlab_output.pdf'):
            file_size = os.path.getsize('test_reportlab_output.pdf')
            print(f"✅ 文件验证成功，大小: {file_size} bytes")
        else:
            print("❌ 文件验证失败")
    else:
        print("❌ PDF生成失败")

if __name__ == "__main__":
    main()
    print("\n测试完成。如果成功，应该生成了 test_reportlab_output.pdf 文件。")
