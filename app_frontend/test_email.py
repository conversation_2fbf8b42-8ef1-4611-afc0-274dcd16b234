#!/usr/bin/env python
"""
邮件系统测试脚本
用于测试邮件发送功能
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.conf import settings
from api.email_utils import send_verification_email, send_password_reset_email, send_welcome_email


def test_email_settings():
    """测试邮件设置"""
    print("📧 邮件设置检查")
    print("=" * 30)
    
    print(f"EMAIL_BACKEND: {getattr(settings, 'EMAIL_BACKEND', '未设置')}")
    print(f"EMAIL_HOST: {getattr(settings, 'EMAIL_HOST', '未设置')}")
    print(f"EMAIL_PORT: {getattr(settings, 'EMAIL_PORT', '未设置')}")
    print(f"EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', '未设置')}")
    print(f"EMAIL_HOST_USER: {getattr(settings, 'EMAIL_HOST_USER', '未设置')}")
    print(f"DEFAULT_FROM_EMAIL: {getattr(settings, 'DEFAULT_FROM_EMAIL', '未设置')}")
    
    if hasattr(settings, 'EMAIL_HOST_PASSWORD'):
        print(f"EMAIL_HOST_PASSWORD: {'已设置' if settings.EMAIL_HOST_PASSWORD else '未设置'}")
    else:
        print("EMAIL_HOST_PASSWORD: 未设置")


def test_verification_email():
    """测试验证邮件"""
    print("\n📧 测试验证邮件")
    print("=" * 30)
    
    email = input("请输入测试邮箱地址: ").strip()
    if not email:
        print("❌ 邮箱地址不能为空")
        return
    
    try:
        # 创建测试用户
        from api.models import CustomUser
        user, created = CustomUser.objects.get_or_create(
            email=email,
            defaults={
                'first_name': '测试',
                'last_name': '用户',
                'is_active': False
            }
        )
        
        if created:
            print(f"✅ 创建测试用户: {email}")
        else:
            print(f"✅ 使用现有用户: {email}")
        
        # 发送验证邮件
        result = send_verification_email(email)
        if result:
            print("✅ 验证邮件发送成功")
        else:
            print("❌ 验证邮件发送失败")
            
    except Exception as e:
        print(f"❌ 发送验证邮件时出错: {e}")


def test_password_reset_email():
    """测试密码重置邮件"""
    print("\n🔐 测试密码重置邮件")
    print("=" * 30)
    
    email = input("请输入测试邮箱地址: ").strip()
    if not email:
        print("❌ 邮箱地址不能为空")
        return
    
    try:
        # 检查用户是否存在
        from api.models import CustomUser
        try:
            user = CustomUser.objects.get(email=email)
            print(f"✅ 找到用户: {email}")
        except CustomUser.DoesNotExist:
            print(f"⚠️  用户不存在，创建测试用户: {email}")
            user = CustomUser.objects.create_user(
                email=email,
                password='testpassword123',
                first_name='测试',
                last_name='用户',
                is_active=True
            )
        
        # 发送密码重置邮件
        import secrets
        token = secrets.token_urlsafe(32)
        result = send_password_reset_email(email, token)
        
        if result:
            print("✅ 密码重置邮件发送成功")
            print(f"🔑 重置令牌: {token}")
        else:
            print("❌ 密码重置邮件发送失败")
            
    except Exception as e:
        print(f"❌ 发送密码重置邮件时出错: {e}")


def test_welcome_email():
    """测试欢迎邮件"""
    print("\n🎉 测试欢迎邮件")
    print("=" * 30)
    
    email = input("请输入测试邮箱地址: ").strip()
    if not email:
        print("❌ 邮箱地址不能为空")
        return
    
    try:
        result = send_welcome_email(email)
        if result:
            print("✅ 欢迎邮件发送成功")
        else:
            print("❌ 欢迎邮件发送失败")
            
    except Exception as e:
        print(f"❌ 发送欢迎邮件时出错: {e}")


def main():
    """主函数"""
    print("🧠 认知健康 - 邮件系统测试")
    print("=" * 40)
    
    while True:
        print("\n📋 选择测试项目:")
        print("1. 检查邮件设置")
        print("2. 测试验证邮件")
        print("3. 测试密码重置邮件")
        print("4. 测试欢迎邮件")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == '1':
            test_email_settings()
        elif choice == '2':
            test_verification_email()
        elif choice == '3':
            test_password_reset_email()
        elif choice == '4':
            test_welcome_email()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")
    
    print("\n💡 提示:")
    print("- 开发环境下，邮件会输出到控制台")
    print("- 生产环境需要配置SMTP设置")
    print("- 检查垃圾邮件文件夹")


if __name__ == "__main__":
    main()
