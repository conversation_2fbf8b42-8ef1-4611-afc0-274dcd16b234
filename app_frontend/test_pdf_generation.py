#!/usr/bin/env python3
"""
测试PDF生成功能
"""

import json
from datetime import datetime

# 模拟测试数据
test_data = {
    "filename": "test_audio.wav",
    "age": "65",
    "relationship": "my_self",
    "occupation": "retired",
    "uploaded_at": "2025-01-17T10:30:00Z",
    "result": json.dumps({
        "Predicted mmse score": 24.5,
        "Percentile": 75,
        "Model": "Advanced Neural Network",
        "Model performance": {
            "RMSE": 2.34,
            "Pearson correlation coefficient": 0.87
        },
        "Transcribed": "The quick brown fox jumps over the lazy dog. This is a test transcription.",
        "Transcribed with color": '<span class="word-normal">The quick brown fox</span> <span class="word-hesitation">jumps</span> over the lazy dog.',
        "Selected features": {
            "prosodic": [
                {
                    "feature name": "speaking_rate",
                    "value": 0.00123,
                    "clsi lower": 0.001,
                    "clsi upper": 0.002,
                    "brief introduction": "Rate of speech measured in syllables per second"
                },
                {
                    "feature name": "pause_duration",
                    "value": 1234,
                    "clsi lower": 1000,
                    "clsi upper": 2000,
                    "brief introduction": "Average duration of pauses in milliseconds"
                }
            ],
            "linguistic": [
                {
                    "feature name": "honore_statistic",
                    "value": 0.000456,
                    "clsi lower": 0.0003,
                    "clsi upper": 0.0006,
                    "brief introduction": "Measure of lexical diversity in speech"
                }
            ]
        }
    })
}

print("测试数据生成完成:")
print("=" * 50)
print(f"文件名: {test_data['filename']}")
print(f"年龄: {test_data['age']}")
print(f"关系: {test_data['relationship']}")
print(f"职业: {test_data['occupation']}")

# 解析结果数据
result_data = json.loads(test_data['result'])
print(f"MMSE分数: {result_data['Predicted mmse score']}")
print(f"百分位: {result_data['Percentile']}")
print(f"模型: {result_data['Model']}")

print("\n特征数据:")
for category, features in result_data['Selected features'].items():
    print(f"  {category}: {len(features)} 个特征")
    for feature in features:
        print(f"    - {feature['feature name']}: {feature['value']}")

print("\n数值格式化测试:")
test_values = [0.00123, 1234, 0.000456, 24.5, 2.34, 0.87]
for value in test_values:
    if value >= 1000 or (value < 0.01 and value != 0):
        scientific = f"{value:.2e}"
        mantissa, exponent = scientific.split('e')
        formatted = f"{mantissa} × 10^{int(exponent)}"
        print(f"  {value} → {formatted} (科学记数法)")
    else:
        formatted = f"{float(f'{value:.3g}'):g}"
        print(f"  {value} → {formatted} (3位有效数字)")

print("\n测试数据已准备就绪，可用于PDF生成测试。")
