#!/usr/bin/env python3
"""
测试xhtml2pdf PDF生成功能
"""

import json
from datetime import datetime
from xhtml2pdf import pisa
from io import BytesIO

# 模拟测试数据
test_data = {
    "filename": "test_audio.wav",
    "age": "65",
    "relationship": "my_self",
    "occupation": "retired",
    "uploaded_at": "2025-01-17T10:30:00Z",
    "result": json.dumps({
        "Predicted mmse score": 24.5,
        "Percentile": 75,
        "Model": "Advanced Neural Network",
        "Model performance": {
            "RMSE": 2.34,
            "Pearson correlation coefficient": 0.87
        },
        "Transcribed": "The quick brown fox jumps over the lazy dog. This is a test transcription for PDF generation.",
        "Transcribed with color": '<span class="word-normal">The quick brown fox</span> <span class="word-hesitation">jumps</span> over the lazy dog.',
        "Selected features": {
            "prosodic": [
                {
                    "feature name": "speaking_rate",
                    "value": 0.00123,
                    "clsi lower": 0.001,
                    "clsi upper": 0.002,
                    "brief introduction": "Rate of speech measured in syllables per second"
                },
                {
                    "feature name": "pause_duration",
                    "value": 1234,
                    "clsi lower": 1000,
                    "clsi upper": 2000,
                    "brief introduction": "Average duration of pauses in milliseconds"
                }
            ],
            "linguistic": [
                {
                    "feature name": "honore_statistic",
                    "value": 0.000456,
                    "clsi lower": 0.0003,
                    "clsi upper": 0.0006,
                    "brief introduction": "Measure of lexical diversity in speech"
                }
            ]
        }
    })
}

# 创建简单的HTML模板用于测试
html_template = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test PDF</title>
    <style>
        @page {{
            size: A4;
            margin: 2cm;
        }}
        body {{ 
            font-family: 'Times New Roman', serif; 
            margin: 0; 
            font-size: 12pt;
            line-height: 1.4;
        }}
        h1 {{ color: #000; text-align: center; font-size: 18pt; }}
        .test-section {{ 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ccc; 
            page-break-inside: avoid;
        }}
        .scientific {{ font-size: 12pt; }}
        sup {{ font-size: 0.8em; vertical-align: super; }}
        table {{ 
            width: 100%; 
            border-collapse: collapse; 
            margin: 10px 0;
        }}
        th, td {{ 
            border: 1px solid #ccc; 
            padding: 8px; 
            text-align: left;
        }}
        th {{ background-color: #f0f0f0; font-weight: bold; }}
    </style>
</head>
<body>
    <h1>Cognitive Health Speech Analysis Report</h1>
    
    <div class="test-section">
        <h2>Patient Information</h2>
        <table>
            <tr>
                <td><strong>File Name:</strong></td>
                <td>{filename}</td>
                <td><strong>Age:</strong></td>
                <td>{age}</td>
            </tr>
            <tr>
                <td><strong>Relationship:</strong></td>
                <td>{relationship}</td>
                <td><strong>Occupation:</strong></td>
                <td>{occupation}</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>Analysis Results</h2>
        <table>
            <tr>
                <th>Predicted MMSE Score</th>
                <th>Percentile</th>
                <th>Model</th>
            </tr>
            <tr>
                <td style="text-align: center; font-weight: bold;">{mmse_score}</td>
                <td style="text-align: center; font-weight: bold;">{percentile}%</td>
                <td style="text-align: center;">{model}</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>Speech Transcription</h2>
        <p style="font-style: italic; background: #f8f8f8; padding: 15px; border-left: 4px solid #000;">
            {transcription}
        </p>
    </div>
    
    <div class="test-section">
        <h2>Scientific Notation Test</h2>
        <table>
            <tr>
                <th>Original Value</th>
                <th>Formatted (Scientific Notation)</th>
                <th>Type</th>
            </tr>
            <tr>
                <td>0.00123</td>
                <td class="scientific">1.23 × 10<sup>-3</sup></td>
                <td>Small number</td>
            </tr>
            <tr>
                <td>1234</td>
                <td class="scientific">1.23 × 10<sup>3</sup></td>
                <td>Large number</td>
            </tr>
            <tr>
                <td>24.5</td>
                <td>24.5</td>
                <td>Normal number</td>
            </tr>
        </table>
    </div>
    
    <div style="margin-top: 50px; text-align: center; color: #666; font-size: 10pt;">
        <p><strong>MEDICAL DISCLAIMER:</strong> This is a test report generated for demonstration purposes.</p>
        <p>Generated: {current_time}</p>
    </div>
</body>
</html>
"""

def render_to_pdf_test(html_content):
    """测试PDF生成函数"""
    result = BytesIO()
    pdf = pisa.pisaDocument(BytesIO(html_content.encode("UTF-8")), result)
    if not pdf.err:
        return result.getvalue()
    return None

try:
    # 解析结果数据
    result_data = json.loads(test_data['result'])
    
    # 填充模板数据
    html_content = html_template.format(
        filename=test_data['filename'],
        age=test_data['age'],
        relationship=test_data['relationship'],
        occupation=test_data['occupation'],
        mmse_score=result_data['Predicted mmse score'],
        percentile=result_data['Percentile'],
        model=result_data['Model'],
        transcription=result_data['Transcribed'],
        current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )
    
    print("正在生成PDF测试文件...")
    
    # 使用xhtml2pdf生成PDF
    pdf_bytes = render_to_pdf_test(html_content)
    
    if pdf_bytes:
        # 保存PDF文件
        with open('test_xhtml2pdf_output.pdf', 'wb') as f:
            f.write(pdf_bytes)
        
        print("✅ PDF生成成功!")
        print(f"📄 文件大小: {len(pdf_bytes)} bytes")
        print("📁 文件保存为: test_xhtml2pdf_output.pdf")
        
        # 验证文件是否存在
        import os
        if os.path.exists('test_xhtml2pdf_output.pdf'):
            file_size = os.path.getsize('test_xhtml2pdf_output.pdf')
            print(f"✅ 文件验证成功，大小: {file_size} bytes")
        else:
            print("❌ 文件验证失败")
    else:
        print("❌ PDF生成失败")

except Exception as e:
    print(f"❌ PDF生成失败: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成。如果成功，应该生成了 test_xhtml2pdf_output.pdf 文件。")
