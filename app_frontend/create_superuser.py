#!/usr/bin/env python
"""
创建超级用户脚本
用于快速创建管理员账户
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from api.models import CustomUser


def create_superuser():
    """创建超级用户"""
    print("🧠 认知健康 - 创建超级用户")
    print("=" * 40)
    
    # 获取用户输入
    email = input("请输入管理员邮箱: ").strip()
    if not email:
        print("❌ 邮箱不能为空")
        return
    
    # 检查邮箱是否已存在
    if CustomUser.objects.filter(email=email).exists():
        print(f"❌ 邮箱 {email} 已存在")
        return
    
    password = input("请输入密码: ").strip()
    if not password:
        print("❌ 密码不能为空")
        return
    
    first_name = input("请输入名字 (可选): ").strip()
    last_name = input("请输入姓氏 (可选): ").strip()
    
    try:
        # 创建超级用户
        user = CustomUser.objects.create_superuser(
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name
        )
        
        print(f"✅ 超级用户创建成功！")
        print(f"   邮箱: {user.email}")
        print(f"   姓名: {user.full_name or '未设置'}")
        print(f"   ID: {user.id}")
        print(f"   创建时间: {user.date_joined}")
        print("\n🚀 现在您可以使用此账户登录管理后台：")
        print(f"   管理后台: http://**************:8001/admin/")
        print(f"   前端登录: http://**************:8000/login/")
        
    except Exception as e:
        print(f"❌ 创建超级用户失败: {e}")


if __name__ == "__main__":
    create_superuser()
