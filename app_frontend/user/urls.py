from django.urls import path
from django.shortcuts import render
from .views import (
    profile_view, settings_view, settings_test_view, config_debug_view, avatar_test_view, password_test_view, user_profile_api,
    upload_avatar_api, update_profile_api, change_password_api
)

# 认证相关URL已移至API应用 (/api/)
# 包括：注册、登录、登出、邮箱验证、密码重置等

urlpatterns = [
    # 用户页面（保留）
    path('profile/', profile_view, name='profile'),
    path('settings/', settings_view, name='settings'),
    path('settings-test/', settings_test_view, name='settings_test'),
    path('config-debug/', config_debug_view, name='config_debug'),
    path('avatar-test/', avatar_test_view, name='avatar_test'),
    path('password-test/', password_test_view, name='password_test'),

    # API端点
    path('api/profile/', user_profile_api, name='user_profile_api'),
    path('api/avatar/', upload_avatar_api, name='upload_avatar_api'),
    path('api/update/', update_profile_api, name='update_profile_api'),
    path('api/change-password/', change_password_api, name='change_password_api'),
]
