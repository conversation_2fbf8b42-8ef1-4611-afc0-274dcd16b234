<!DOCTYPE html>
<html>
<head>
    <title>Test <PERSON>ton Layout</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            background-color: #EAEAEA;
            margin: 0;
            padding: 2rem;
        }

        .controls {
            max-width: 900px;
            margin: 0 auto 1.5rem auto;
            display: flex;
            justify-content: space-between;
            gap: 1rem;
            border: 2px dashed #ccc;
            padding: 1rem;
            background-color: #f9f9f9;
        }

        .control-btn {
            font-family: 'Times New Roman', Times, serif;
            text-decoration: none;
            color: #000;
            background-color: #FFFFFF;
            border: 1px solid #000;
            padding: 0.5rem 1rem;
            border-radius: 0;
            font-size: 0.9rem;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: background-color 0.2s;
        }
        
        .control-btn:hover {
            background-color: #F0F0F0;
        }

        .demo-content {
            max-width: 900px;
            margin: 0 auto;
            background: #FFFFFF;
            border: 1px solid #000000;
            padding: 2rem;
            text-align: center;
        }

        @media (max-width: 800px) {
            .controls { 
                justify-content: space-between;
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <h1 style="text-align: center;">按钮布局测试</h1>
    
    <div class="controls">
        <a href="#" class="control-btn">
            <i class="fa-solid fa-arrow-left"></i> Back to History
        </a>
        <button class="control-btn">
            <i class="fa-solid fa-file-pdf"></i> Download PDF Report
        </button>
    </div>

    <div class="demo-content">
        <h2>布局说明</h2>
        <p><strong>期望效果：</strong></p>
        <ul style="text-align: left; max-width: 500px; margin: 0 auto;">
            <li><strong>Back to History</strong> 按钮应该在左边</li>
            <li><strong>Download PDF Report</strong> 按钮应该在右边</li>
            <li>两个按钮之间有适当的间距</li>
            <li>在小屏幕上保持相同的布局</li>
        </ul>
        
        <p style="margin-top: 2rem;"><strong>CSS 设置：</strong></p>
        <code style="background: #f0f0f0; padding: 0.5rem; display: inline-block;">
            justify-content: space-between;
        </code>
    </div>
</body>
</html>
