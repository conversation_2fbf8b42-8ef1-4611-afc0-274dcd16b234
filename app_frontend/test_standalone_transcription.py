#!/usr/bin/env python3
"""
独立测试重新实现的Enhanced Transcription功能 - 使用最流行的编程方式
"""

import json
from datetime import datetime
from xhtml2pdf import pisa
from io import BytesIO

# 测试数据
test_data = {
    "filename": "standalone_transcription_test.wav",
    "age": "75",
    "relationship": "my_self",
    "occupation": "retired",
    "uploaded_at": "2025-01-17T10:30:00Z",
    "result": json.dumps({
        "Predicted mmse score": 20.5,
        "Percentile": 58,
        "Model": "Advanced Neural Network v2.1",
        "Model performance": {
            "RMSE": 2.67,
            "Pearson correlation coefficient": 0.83
        },
        "Transcribed": "The patient spoke clearly about their daily activities and mentioned some memory concerns during our conversation.",
        "Transcribed with color": '<span class="word-normal">The patient spoke clearly about their daily activities and mentioned some</span> <span class="word-hesitation">memory</span> <span class="word-normal">concerns during our</span> <span class="word-pause">[pause]</span> <span class="word-normal">conversation and</span> <span class="word-filler">uh</span> <span class="word-normal">also discussed</span> <span class="word-repetition">future future</span> <span class="word-normal">plans including</span> <span class="word-prolongation">looong</span> <span class="word-normal">term goals and</span> <span class="word-unclear">[unclear speech]</span> <span class="word-normal">daily routines.</span>',
        "Selected features": {
            "digipsych_prosody_feats": [
                {
                    "feature name": "speaking_rate",
                    "value": 0.00115,
                    "clsi lower": 0.001,
                    "clsi upper": 0.002,
                    "brief introduction": "Rate of speech measured in syllables per second"
                }
            ]
        }
    })
}

def format_relationship(value):
    """格式化关系"""
    if not value:
        return 'Not provided'
    relationship_map = {
        'my_self': 'Myself',
        'my_father': 'My Father',
        'my_mother': 'My Mother',
        'my_father_in_law': 'My Father in Law',
        'my_mother_in_law': 'My Mother in Law',
        'my_grandfather': 'My Grandfather',
        'my_grandmother': 'My Grandmother',
        'my_friend': 'My Friend'
    }
    return relationship_map.get(value, value)

def format_occupation(value):
    """格式化职业"""
    if not value:
        return 'Not provided'
    occupation_map = {
        'student': 'Student',
        'retired': 'Retired',
        'unemployed': 'Unemployed'
    }
    return occupation_map.get(value, value.replace('_', ' ').title())

def format_number(value):
    """格式化数值"""
    if value is None:
        return 'Not provided'
    
    try:
        num = float(value)
        if num == 0:
            return '0'
        if num == int(num) and abs(num) < 1000:
            return str(int(num))
        
        abs_num = abs(num)
        if abs_num >= 1000 or (abs_num < 0.01 and abs_num != 0):
            scientific_str = f"{num:.2e}"
            mantissa, exponent_str = scientific_str.split('e')
            exponent = int(exponent_str)
            return f"{mantissa} × 10^{exponent}"
        
        return f"{float(f'{num:.3g}'):g}"
    except (ValueError, TypeError):
        return str(value)

def process_colored_transcription(html_text):
    """处理彩色转录文本 - 最流行的字符串处理方式"""
    if not html_text:
        return '<span style="color: #333;">No transcription available</span>'
    
    print(f"处理前: {html_text}")
    
    # 定义颜色映射 - 直接使用内联样式
    color_replacements = {
        'word-normal': 'color: #333333;',
        'word-hesitation': 'color: #ff6b35; font-weight: bold;',
        'word-repetition': 'color: #e74c3c; font-weight: bold; text-decoration: underline;',
        'word-pause': 'color: #9b59b6; font-weight: bold;',
        'word-filler': 'color: #f39c12; font-style: italic;',
        'word-prolongation': 'color: #3498db;',
        'word-unclear': 'color: #95a5a6; text-decoration: line-through;'
    }
    
    # 处理HTML文本
    result = html_text
    
    # 替换每个class为内联样式
    for class_name, style in color_replacements.items():
        # 替换 <span class="word-xxx"> 为 <span style="...">
        old_pattern = f'<span class="{class_name}">'
        new_pattern = f'<span style="{style}">'
        result = result.replace(old_pattern, new_pattern)
        print(f"替换 {class_name}: {old_pattern in html_text} -> {new_pattern in result}")
    
    # 清理可能的多余空格
    result = ' '.join(result.split())
    
    print(f"处理后: {result}")
    return result

def generate_transcription_html(analysis_result):
    """生成转录文本HTML - 使用最流行的编程方式"""
    try:
        # 获取原始转录文本
        transcribed = analysis_result.get('Transcribed', '')
        transcribed_with_color = analysis_result.get('Transcribed with color', '')
        
        print(f"原始转录文本: {transcribed}")
        print(f"彩色转录文本: {transcribed_with_color}")
        
        # 如果没有转录文本，返回默认消息
        if not transcribed and not transcribed_with_color:
            return '''
            <div class="speech-analysis-section">
                <h3>Speech Analysis</h3>
                <div class="transcription-container">
                    <h4>Enhanced Transcription with Linguistic Analysis</h4>
                    <div class="transcription-content">
                        <p style="text-align: center; color: #666; font-style: italic;">
                            No transcription data available for this analysis.
                        </p>
                    </div>
                </div>
            </div>
            '''
        
        # 处理彩色转录文本
        if transcribed_with_color and transcribed_with_color.strip():
            # 使用彩色转录文本
            processed_text = process_colored_transcription(transcribed_with_color)
        else:
            # 使用普通转录文本
            processed_text = f'<span style="color: #333;">{transcribed}</span>'
        
        # 生成完整的HTML
        html_content = f'''
        <div class="speech-analysis-section">
            <h3>Speech Analysis</h3>
            <div class="transcription-container">
                <h4>Enhanced Transcription with Linguistic Analysis</h4>
                <div class="transcription-content">
                    {processed_text}
                </div>
                <div class="color-legend">
                    <strong>Color Legend:</strong>
                    <span style="color: #333;">■ Normal speech</span> |
                    <span style="color: #ff6b35; font-weight: bold;">■ Hesitations</span> |
                    <span style="color: #e74c3c; font-weight: bold; text-decoration: underline;">■ Repetitions</span> |
                    <span style="color: #9b59b6; font-weight: bold;">■ Pauses</span> |
                    <span style="color: #f39c12; font-style: italic;">■ Fillers</span> |
                    <span style="color: #3498db;">■ Prolongations</span> |
                    <span style="color: #95a5a6; text-decoration: line-through;">■ Unclear</span>
                </div>
            </div>
        </div>
        '''
        
        return html_content
        
    except Exception as e:
        # 错误处理 - 返回错误信息
        return f'''
        <div class="speech-analysis-section">
            <h3>Speech Analysis</h3>
            <div class="transcription-container">
                <h4>Enhanced Transcription with Linguistic Analysis</h4>
                <div class="transcription-content">
                    <p style="color: #e74c3c; font-style: italic;">
                        Error processing transcription: {str(e)}
                    </p>
                </div>
            </div>
        </div>
        '''

def main():
    try:
        print("开始测试独立的Enhanced Transcription功能...\n")
        
        # 解析测试数据
        analysis_result = json.loads(test_data['result'])
        
        print("=== 步骤1: 测试转录文本处理 ===")
        transcription_html = generate_transcription_html(analysis_result)
        print(f"生成的HTML长度: {len(transcription_html)} 字符")
        
        # 检查关键内容
        key_elements = [
            'Speech Analysis',
            'Enhanced Transcription with Linguistic Analysis',
            'memory',
            'conversation',
            'color: #ff6b35',
            'Color Legend'
        ]
        
        print("\nHTML内容检查:")
        for element in key_elements:
            found = element in transcription_html
            status = "✅" if found else "❌"
            print(f"  {status} 包含 '{element}': {found}")
        
        print(f"\n=== 步骤2: 生成完整PDF ===")
        
        # 获取模型性能数据
        model_performance = analysis_result.get('Model performance', {})
        rmse = model_performance.get('RMSE')
        pearson = model_performance.get('Pearson correlation coefficient')
        rmse_formatted = format_number(rmse) if rmse is not None else 'Not provided'
        pearson_formatted = format_number(pearson) if pearson is not None else 'Not provided'
        
        # 创建完整的HTML
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{ size: A4; margin: 1.5cm; }}
                body {{ font-family: 'Times New Roman', Times, serif; font-size: 11pt; line-height: 1.4; color: #333; }}
                .report-header {{ text-align: center; margin-bottom: 25px; border-bottom: 2px solid #000; padding-bottom: 15px; }}
                .title {{ font-size: 18pt; font-weight: bold; margin-bottom: 8px; }}
                .subtitle {{ font-size: 11pt; font-weight: bold; margin-bottom: 15px; color: #666; }}
                .section-title {{ font-size: 13pt; font-weight: bold; margin-top: 20px; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 3px; }}
                table {{ width: 100%; border-collapse: collapse; margin-bottom: 15px; }}
                .demographics-table td {{ border: 1px solid #000; padding: 6px 8px; font-size: 10pt; }}
                .demographics-table .label {{ background-color: #f0f0f0; font-weight: bold; width: 25%; }}
                .results-table th {{ background-color: #f0f0f0; border: 1px solid #000; padding: 8px; font-weight: bold; text-align: center; font-size: 10pt; }}
                .results-table td {{ border: 1px solid #000; padding: 8px; text-align: center; font-size: 11pt; }}
                
                /* 语音分析样式 */
                .speech-analysis-section {{ margin: 20px 0; }}
                .speech-analysis-section h3 {{ font-size: 13pt; font-weight: bold; margin-top: 20px; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 3px; }}
                .transcription-container {{ margin: 15px 0; }}
                .transcription-container h4 {{ font-size: 11pt; font-weight: bold; margin-bottom: 10px; }}
                .transcription-content {{ background-color: #f7f7f7; border-left: 4px solid #000; padding: 15px; margin: 10px 0; font-style: italic; font-size: 11pt; line-height: 1.6; min-height: 60px; }}
                .color-legend {{ font-size: 9pt; margin: 10px 0; padding: 8px; background-color: #f9f9f9; border: 1px solid #ddd; }}
                
                .disclaimer {{ font-size: 8pt; margin-top: 25px; text-align: justify; padding: 10px; background-color: #f9f9f9; border: 1px solid #ddd; }}
            </style>
        </head>
        <body>
            <div class="report-header">
                <div class="title">Cognitive Health Speech Analysis</div>
                <div class="subtitle">CONFIDENTIAL SCIENTIFIC REPORT</div>
            </div>
            
            <div class="section-title">Subject Demographics</div>
            <table class="demographics-table">
                <tr>
                    <td class="label">Audio File Name:</td>
                    <td>{test_data.get('filename', 'N/A')}</td>
                    <td class="label">Date of Report:</td>
                    <td>{datetime.now().strftime('%d-%m-%Y')}</td>
                </tr>
                <tr>
                    <td class="label">This Audio is Spoken by:</td>
                    <td>{format_relationship(test_data.get('relationship'))}</td>
                    <td class="label">Occupation:</td>
                    <td>{format_occupation(test_data.get('occupation'))}</td>
                </tr>
                <tr>
                    <td class="label">Age:</td>
                    <td>{test_data.get('age', 'Not provided')}</td>
                    <td class="label">Upload Date:</td>
                    <td>{datetime.fromisoformat(test_data.get('uploaded_at', datetime.now().isoformat())).strftime('%d-%m-%Y')}</td>
                </tr>
            </table>
            
            <div class="section-title">Test Results</div>
            <table class="results-table">
                <tr>
                    <th style="width: 50%;">Predicted MMSE Score<br/><small>(from voice analysis)</small></th>
                    <th style="width: 50%;">Percentile<br/><small>(compared to age-matched population)</small></th>
                </tr>
                <tr>
                    <td style="font-weight: bold; font-size: 12pt;">{format_number(analysis_result.get('Predicted mmse score'))}</td>
                    <td style="font-weight: bold; font-size: 12pt;">{analysis_result.get('Percentile', 'Not provided')}%</td>
                </tr>
            </table>
            
            {transcription_html}
            
            <div class="section-title">Methodology</div>
            <table class="results-table">
                <tr>
                    <th style="width: 30%;">Model</th>
                    <th style="width: 35%;">RMSE</th>
                    <th style="width: 35%;">Pearson Correlation</th>
                </tr>
                <tr>
                    <td style="font-weight: bold;">{analysis_result.get('Model', 'Not provided')}</td>
                    <td style="font-weight: bold;">{rmse_formatted}</td>
                    <td style="font-weight: bold;">{pearson_formatted}</td>
                </tr>
            </table>
            
            <div class="disclaimer">
                <strong>MEDICAL DISCLAIMER:</strong> This computational analysis report is generated using artificial intelligence algorithms for research and clinical decision support purposes. The acoustic and linguistic biomarkers presented herein are derived from automated speech analysis and should be interpreted within the context of comprehensive clinical assessment.
            </div>
        </body>
        </html>
        """
        
        print("正在生成PDF...")
        
        # 生成PDF
        result = BytesIO()
        pdf = pisa.pisaDocument(BytesIO(html_content.encode("UTF-8")), result)
        
        if not pdf.err:
            pdf_bytes = result.getvalue()
            
            # 保存PDF文件
            with open('test_standalone_transcription.pdf', 'wb') as f:
                f.write(pdf_bytes)
            
            print("✅ 独立Enhanced Transcription功能测试成功!")
            print(f"📄 文件大小: {len(pdf_bytes)} bytes")
            print("📁 文件保存为: test_standalone_transcription.pdf")
            
            # 验证文件是否存在
            import os
            if os.path.exists('test_standalone_transcription.pdf'):
                file_size = os.path.getsize('test_standalone_transcription.pdf')
                print(f"✅ 文件验证成功，大小: {file_size} bytes")
                
                # 检查PDF内容
                with open('test_standalone_transcription.pdf', 'rb') as f:
                    pdf_content = f.read()
                
                keywords = [
                    b'Speech Analysis',
                    b'Enhanced Transcription',
                    b'memory',
                    b'conversation',
                    b'Color Legend'
                ]
                
                print(f"\nPDF内容检查:")
                for keyword in keywords:
                    found = keyword in pdf_content
                    status = "✅" if found else "❌"
                    print(f"  {status} 包含 '{keyword.decode()}': {found}")
                
                print(f"\n🎯 重新实现成功！")
                print("📋 新实现的特点:")
                print("   🔧 使用最流行的字符串替换方法")
                print("   🎨 直接使用内联CSS样式")
                print("   📝 简单清晰的HTML结构")
                print("   🛡️ 完善的错误处理机制")
                print("   ✨ 更好的兼容性和可靠性")
                
            else:
                print("❌ 文件验证失败")
        else:
            print("❌ PDF生成失败，存在错误")
            print(f"错误信息: {pdf.err}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    print("\n🚀 现在PDF中的Enhanced Transcription with Linguistic Analysis应该正确显示！")
