from django.shortcuts import render
from project.settings import API_BASE_URL, LOCAL_BASE_URL
import json
import numpy as np
import os
import tempfile
from django.http import HttpResponse
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from io import BytesIO
from datetime import datetime
from xhtml2pdf import pisa
import base64



class NpEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (np.int64, np.int32, np.float32, np.float64)):
            return int(obj) if isinstance(obj, (np.int64, np.int32)) else float(obj)
        elif isinstance(obj, np.ndar<PERSON>):
            return obj.tolist()
        return super(Np<PERSON>nco<PERSON>, self).default(obj)

def format_relationship(value):
    """格式化关系 - 与详情页完全一致"""
    if not value:
        return 'Not provided'
    relationship_map = {
        'my_self': 'Myself',
        'my_father': 'My Father',
        'my_mother': 'My Mother',
        'my_father_in_law': 'My Father in Law',
        'my_mother_in_law': 'My Mother in Law',
        'my_grandfather': 'My Grandfather',
        'my_grandmother': 'My Grandmother',
        'my_friend': 'My Friend'
    }
    return relationship_map.get(value, value)

def format_occupation(value):
    """格式化职业 - 与详情页完全一致"""
    if not value:
        return 'Not provided'
    occupation_map = {
        'student': 'Student',
        'retired': 'Retired',
        'unemployed': 'Unemployed'
    }
    return occupation_map.get(value, value.replace('_', ' ').title())

def format_number(value):
    """标准的数值格式化函数 - 与详情页完全一致"""
    if value is None:
        return 'Not provided'

    try:
        num = float(value)

        # 处理零值
        if num == 0:
            return '0'

        # 处理整数
        if num == int(num) and abs(num) < 1000:
            return str(int(num))

        abs_num = abs(num)

        # 判断是否需要科学记数法：过大(≥1000)或过小(<0.01)的数值
        if abs_num >= 1000 or (abs_num < 0.01 and abs_num != 0):
            # 使用标准的科学记数法格式
            scientific_str = f"{num:.2e}"  # 保留2位小数，总共3位有效数字
            mantissa, exponent_str = scientific_str.split('e')
            exponent = int(exponent_str)

            # 格式化为标准的10为底科学记数法
            return f"{mantissa} × 10^{exponent}"

        # 对于正常范围的数值，保留3位有效数字
        return f"{float(f'{num:.3g}'):g}"
    except (ValueError, TypeError):
        return str(value)



def format_number_for_pdf(value):
    """为PDF格式化数值，使用HTML上标"""
    if value is None:
        return 'Not provided'

    try:
        num = float(value)

        # 处理零值
        if num == 0:
            return '0'

        # 处理整数
        if num == int(num) and abs(num) < 1000:
            return str(int(num))

        abs_num = abs(num)

        # 判断是否需要科学记数法：过大(≥1000)或过小(<0.01)的数值
        if abs_num >= 1000 or (abs_num < 0.01 and abs_num != 0):
            # 使用标准的科学记数法格式，保留3位有效数字
            scientific_str = f"{num:.2e}"  # 保留2位小数
            mantissa, exponent_str = scientific_str.split('e')
            exponent = int(exponent_str)

            # 格式化为标准的10为底科学记数法，使用HTML上标
            return f"{mantissa} × 10<sup>{exponent}</sup>"

        # 对于正常范围的数值，保留3位有效数字
        return f"{float(f'{num:.3g}'):g}"
    except (ValueError, TypeError):
        return str(value)

def format_relationship(value):
    """格式化关系字段"""
    if not value:
        return 'Not provided'
    relationship_map = {
        'my_self': 'Myself',
        'my_father': 'My Father',
        'my_mother': 'My Mother',
        'my_father_in_law': 'My Father in Law',
        'my_mother_in_law': 'My Mother in Law',
        'my_grandfather': 'My Grandfather',
        'my_grandmother': 'My Grandmother',
        'my_friend': 'My Friend'
    }
    return relationship_map.get(value, value)

def format_occupation(value):
    """格式化职业字段"""
    if not value:
        return 'Not provided'
    occupation_map = {
        'student': 'Student',
        'retired': 'Retired',
        'unemployed': 'Unemployed'
    }
    return occupation_map.get(value, value.replace('_', ' ').title())

def get_logo_base64():
    """获取logo的base64编码 - 与audio_details.html一致"""
    try:
        import os
        import base64

        # 使用与audio_details.html相同的logo文件
        logo_path = 'audio_upload/static/logos/logo1.png'

        if os.path.exists(logo_path):
            with open(logo_path, 'rb') as f:
                logo_data = f.read()
            return base64.b64encode(logo_data).decode('utf-8')
        else:
            # 如果logo文件不存在，返回空字符串
            return ""
    except Exception:
        return ""

def generate_pdf_with_xhtml2pdf(analysis_data, analysis_result):
    """使用xhtml2pdf生成带有单词上色的PDF报告 - 完全匹配详情页样式"""

    # 获取转录文本
    transcribed_with_color = analysis_result.get('Transcribed with color', '')
    transcribed = analysis_result.get('Transcribed', 'Transcription not available')

    # 使用彩色转录文本，如果没有则使用普通转录
    if transcribed_with_color and transcribed_with_color.strip():
        transcription_content = transcribed_with_color
    else:
        transcription_content = f'<span class="word-normal">{transcribed}</span>'





    # 创建HTML模板 - 完全匹配详情页样式
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Cognitive Health Speech Analysis Report</title>
        <style>
            @page {{
                size: A4;
                margin: 1.5cm;
            }}

            /* 完全复制详情页的样式 */
            body {{
                font-family: 'Times New Roman', Times, serif;
                background-color: #EAEAEA;
                margin: 0;
                padding: 2rem;
                color: #000000;
                -webkit-font-smoothing: auto;
            }}

            .report-page {{
                max-width: 900px;
                margin: 0 auto;
                background: #FFFFFF;
                border: 1px solid #000000;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            }}

            .report-header {{
                padding: 2rem;
                border-bottom: 2px solid #000000;
            }}

            .header-main {{
                text-align: center;
                margin-bottom: 2rem;
            }}

            .header-main h1 {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.8rem;
                margin: 0 0 0.25rem 0;
                font-weight: bold;
                color: #000000;
            }}
            .header-main p {{
                font-size: 1rem;
                margin: 0;
                font-weight: bold;
                color: #333;
            }}

            .demographics-table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 1.1rem;
                border: 1px solid #000;
            }}
            .demographics-table td {{
                padding: 0.35rem 0.5rem;
                border: 1px solid #CCC;
            }}
            .demographics-table .label {{
                font-weight: bold;
                white-space: nowrap;
                color: #000;
            }}
            .demographics-table .value {{
                color: #000;
            }}

            .report-body {{
                padding: 2rem;
                font-family: 'Times New Roman', Times, serif;
            }}

            .report-section {{
                margin-bottom: 3.5rem;
                page-break-inside: avoid;
            }}

            .section-title {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.6rem;
                font-weight: bold;
                color: #000;
                border-bottom: 2px solid #000;
                padding-bottom: 0.8rem;
                margin: 0 0 1.5rem 0;
                padding-top: 1rem;
            }}

            .results-table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 1.1rem;
                margin-bottom: 2.5rem;
                border: 2px solid black;
            }}
            .results-table th, .results-table td {{
                border: 1px solid black;
                padding: 0.5rem;
                text-align: left;
            }}
            .results-table th {{
                font-family: 'Times New Roman', Times, serif;
                background-color: #F0F0F0;
                font-weight: bold;
                font-size: 1.2rem;
            }}
            .results-table .result-value {{
                font-weight: bold;
                text-align: center;
                font-size: 1.2rem;
            }}
            .results-table .result-assessment {{
                 font-style: normal;
            }}

            .transcription-box {{
                background: #F8F8F8;
                border: 2px solid #CCC;
                border-left: 6px solid #000000;
                padding: 1.5rem 2rem;
                margin-top: 1.5rem;
                margin-bottom: 2rem;
            }}
            .transcription-box h4 {{
                font-family: 'Times New Roman', Times, serif;
                font-weight: bold;
                font-size: 1.3rem;
                margin-bottom: 1rem;
            }}
            .transcription-content {{
                margin: 0;
                line-height: 1.8;
                font-style: italic;
                color: #000;
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.1rem;
            }}

            /* 完全匹配详情页的单词上色样式 */
            .transcription-content .word-normal {{
                color: #333;
            }}
            .transcription-content .word-hesitation {{
                color: #ff6b35;
                background-color: rgba(255, 107, 53, 0.1);
                padding: 1px 3px;
                border-radius: 3px;
            }}
            .transcription-content .word-repetition {{
                color: #e74c3c;
                background-color: rgba(231, 76, 60, 0.1);
                padding: 1px 3px;
                border-radius: 3px;
                text-decoration: underline;
            }}
            .transcription-content .word-pause {{
                color: #9b59b6;
                background-color: rgba(155, 89, 182, 0.1);
                padding: 1px 3px;
                border-radius: 3px;
                font-weight: bold;
            }}
            .transcription-content .word-filler {{
                color: #f39c12;
                background-color: rgba(243, 156, 18, 0.1);
                padding: 1px 3px;
                border-radius: 3px;
                font-style: normal;
            }}
            .transcription-content .word-unclear {{
                color: #95a5a6;
                background-color: rgba(149, 165, 166, 0.1);
                padding: 1px 3px;
                border-radius: 3px;
                text-decoration: line-through;
            }}
            .transcription-content .word-emphasis {{
                color: #27ae60;
                background-color: rgba(39, 174, 96, 0.1);
                padding: 1px 3px;
                border-radius: 3px;
                font-weight: bold;
            }}
            .transcription-content .word-error {{
                color: #c0392b;
                background-color: rgba(192, 57, 43, 0.1);
                padding: 1px 3px;
                border-radius: 3px;
                border: 1px solid rgba(192, 57, 43, 0.3);
            }}

            .features-grid {{
                margin-top: 2rem;
            }}
            .feature-category {{
                margin-bottom: 3rem;
                page-break-inside: avoid;
            }}
            .feature-category h4 {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.4rem;
                font-weight: bold;
                margin: 0 0 1.5rem 0;
                color: #000;
                border-bottom: 1px solid #CCC;
                padding-bottom: 0.5rem;
            }}
            .feature-table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 1.1rem;
                margin-bottom: 2rem;
                border: 1px solid #000;
            }}
            .feature-table th, .feature-table td {{
                border: 1px solid #E0E0E0;
                padding: 0.4rem;
                text-align: left;
            }}
            .feature-table th {{
                background-color: #F8F8F8;
                font-weight: bold;
                font-size: 1.2rem;
            }}
            .feature-table td:nth-child(2), .feature-table th:nth-child(2) {{
                text-align: center;
                width: 20%;
                position: relative;
            }}
            .feature-table td:nth-child(3), .feature-table th:nth-child(3) {{
                text-align: center;
                width: 25%;
            }}

            .abnormal-indicator {{
                color: #000000;
                font-weight: bold;
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
            }}

            .reference-range {{
                font-size: 0.85rem;
                color: #555;
                margin-left: 0.5rem;
                font-weight: normal;
            }}

            .report-footer {{
                margin-top: 3rem;
                padding: 1.5rem 2rem;
                border-top: 2px solid #000;
                font-size: 1.0rem;
                color: #333;
            }}
            .disclaimer {{
                line-height: 1.6;
                margin-bottom: 1rem;
            }}
            .contact-info {{
                line-height: 1.6;
                margin-bottom: 1rem;
                margin-top: 1rem;
            }}

            @media print {{
                body {{ background-color: #fff; padding: 0; }}
                .report-page {{
                    box-shadow: none;
                    border: none;
                    max-width: 100%;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="report-page">
            <header class="report-header">
                <div class="header-main">
                    <h1>Cognitive Health Speech Analysis</h1>
                    <p>CONFIDENTIAL SCIENTIFIC REPORT</p>
                </div>
                <table class="demographics-table">
                    <tbody>
                        <tr>
                            <td class="label">Audio File Name:</td>
                            <td class="value">{analysis_data.get('filename', 'N/A')}</td>
                            <td class="label">Date of Report:</td>
                            <td class="value">{datetime.now().strftime('%d-%m-%Y')}</td>
                        </tr>
                        <tr>
                            <td class="label">This Audio is Spoken by:</td>
                            <td class="value">{format_relationship(analysis_data.get('relationship'))}</td>
                            <td class="label">Occupation:</td>
                            <td class="value">{format_occupation(analysis_data.get('occupation'))}</td>
                        </tr>
                        <tr>
                            <td class="label">Age:</td>
                            <td class="value" colspan="3">{analysis_data.get('age', 'N/A')}</td>
                        </tr>
                    </tbody>
                </table>
            </header>

            <main class="report-body">
                <section class="report-section">
                    <h3 class="section-title">Prediction Results</h3>
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th style="width: 50%; text-align: center;">
                                    <strong>Predicted MMSE Score</strong> (from speech)<br>
                                    <small style="font-weight: normal; font-size: 0.9em; color: #666;">
                                        The Mini-Mental State Examination (MMSE) score predicted by analyzing the speech.
                                        An MMSE score of 23 or lower indicates cognitive impairment.
                                    </small>
                                </th>
                                <th style="width: 50%; text-align: center;">
                                    Percentile<br>
                                    <small style="font-weight: normal; font-size: 0.9em; color: #666;">
                                        The percentile ranking of the predicted MMSE scores within the studied population.
                                        The predicted MMSE score exceeds that of {analysis_result.get('Percentile', 'N/A')}% of the studied population.
                                    </small>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="result-value">{format_number_for_pdf(analysis_result.get('Predicted mmse score', 'N/A'))}</td>
                                <td class="result-value">{analysis_result.get('Percentile', 'N/A')}%</td>
                            </tr>
                        </tbody>
                    </table>
                </section>

                <section class="report-section">
                    <h3 class="section-title">Technical Details</h3>
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th style="width: 50%; text-align: center;">
                                    Model<br>
                                    <small style="font-weight: normal; font-size: 0.9em; color: #666;">
                                        The model used to predict the MMSE score.
                                    </small>
                                </th>
                                <th style="width: 50%; text-align: center;">
                                    Model Performance<br>
                                    <small style="font-weight: normal; font-size: 0.9em; color: #666;">
                                        Statistical measures of model accuracy and reliability.
                                    </small>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="result-value">{analysis_result.get('Model', 'N/A')}</td>
                                <td>"""

    # 处理 Model performance 数据 - 完全匹配详情页格式
    model_performance = analysis_result.get('Model performance', {})
    if isinstance(model_performance, dict):
        rmse_value = format_number_for_pdf(model_performance.get('RMSE', 'N/A'))
        pearson_value = format_number_for_pdf(model_performance.get('Pearson correlation coefficient', 'N/A'))
    else:
        rmse_value = format_number_for_pdf(model_performance)
        pearson_value = 'N/A'

    html_template += f"""
                                    <div style="margin-bottom: 15px;">
                                        <strong>RMSE:</strong> {rmse_value}<br>
                                        <small style="color: #666; font-size: 0.9em;">
                                            Root Mean Square Error - indicates the average absolute bias between predicted and actual MMSE scores.
                                            Lower values indicate higher accuracy.
                                        </small>
                                    </div>
                                    <div>
                                        <strong>Pearson correlation coefficient:</strong> {pearson_value}<br>
                                        <small style="color: #666; font-size: 0.9em;">
                                            Measures the correlation between predicted and actual MMSE scores.
                                            Higher values (maximum 1.0) indicate better prediction accuracy.
                                        </small>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </section>

                <section class="report-section">
                    <h3 class="section-title">Speech Analysis</h3>
                    <div class="transcription-box">
                        <h4>Enhanced Transcription with Linguistic Analysis</h4>
                        <p style="margin: 0 0 1rem 0; font-size: 0.95em; color: #666; font-style: normal;">
                            The automatic transcription of {analysis_data.get('filename', 'the audio file')}.
                            The highlighted words were analyzed as crucial for making predictions.
                        </p>
                        <div class="transcription-content">
                            {transcription_content}
                        </div>
                    </div>
                </section>"""

    # 添加特征分析部分 - 完全匹配详情页布局
    features = analysis_result.get('Selected features', {})
    if features:
        html_template += """
                <section class="report-section">
                    <h3 class="section-title">Feature Analysis</h3>
                    <div class="features-grid">"""

        # 处理特征类别 - 与详情页完全一致
        category_keys = list(features.keys())
        # 将 is10 移到最后
        if 'is10' in category_keys:
            category_keys.remove('is10')
            category_keys.append('is10')

        for category in category_keys:
            feature_list = features[category]
            if not isinstance(feature_list, list) or len(feature_list) == 0:
                continue

            # 映射类别名称 - 与详情页完全一致
            category_name = category.replace('_', ' ')
            if category_name == 'digipsych prosody feats':
                category_name = 'Prosodic'
            elif category_name == 'lexicosyntactic':
                category_name = 'Linguistic'
            elif category_name == 'is10':
                category_name = 'Acoustic'

            html_template += f"""
                        <div class="feature-category">
                            <h4>{category_name} Features</h4>
                            <table class="feature-table">
                                <thead>
                                    <tr>
                                        <th>Feature</th>
                                        <th style="text-align: center;">Value</th>
                                        <th style="text-align: center;">Reference Range</th>
                                    </tr>
                                </thead>
                                <tbody>"""

            for feature_obj in feature_list:
                name = feature_obj.get('feature name', 'N/A')
                value = feature_obj.get('value', 'N/A')
                lower = feature_obj.get('clsi lower')
                upper = feature_obj.get('clsi upper')
                brief_intro = feature_obj.get('brief introduction', '')

                display_name = name.replace('_', ' ')

                # 检查是否异常 - 与详情页完全一致
                indicator = ''
                if lower is not None and upper is not None and value != 'N/A':
                    try:
                        num_value = float(value)
                        num_lower = float(lower)
                        num_upper = float(upper)
                        if num_value > num_upper:
                            indicator = ' ▲'
                        elif num_value < num_lower:
                            indicator = ' ▼'
                    except (ValueError, TypeError):
                        pass

                # 格式化参考范围
                if lower is not None and upper is not None:
                    ref_range = f"{format_number_for_pdf(lower)} - {format_number_for_pdf(upper)}"
                else:
                    ref_range = 'Not provided'

                # 特征名称处理 - 包含简介
                feature_name_display = display_name
                if brief_intro:
                    feature_name_display += f"<br><small style='color: #666666; font-style: italic; font-size: 0.9em;'>{brief_intro}</small>"

                html_template += f"""
                                    <tr>
                                        <td>{feature_name_display}</td>
                                        <td style="text-align: center;"><strong>{format_number_for_pdf(value)}</strong>{indicator}</td>
                                        <td style="text-align: center;">{ref_range}</td>
                                    </tr>"""

            html_template += """
                                </tbody>
                            </table>
                        </div>"""

        html_template += """
                    </div>
                </section>"""
    else:
        html_template += """
                <section class="report-section">
                    <h3 class="section-title">Feature Analysis</h3>
                    <p>No biomarker data available for this analysis.</p>
                </section>"""

    # 添加页脚 - 完全匹配详情页样式
    disclaimer_text = "This computational analysis report is generated using artificial intelligence algorithms for research and clinical decision support purposes. The acoustic and linguistic biomarkers presented herein are derived from automated speech analysis and should be interpreted within the context of comprehensive clinical assessment. This report does not constitute a medical diagnosis, clinical recommendation, or therapeutic intervention. The findings require validation through standardized neuropsychological evaluation and clinical correlation by qualified healthcare professionals. Healthcare providers should exercise clinical judgment when integrating these results with patient history, physical examination, and other diagnostic modalities. This technology is intended as an adjunctive tool and should not replace established clinical protocols or professional medical expertise in the assessment of cognitive function."

    html_template += f"""
            </main>

            <footer class="report-footer">
                <p class="disclaimer">
                    <strong>MEDICAL DISCLAIMER:</strong> {disclaimer_text}
                </p>
                <p class="contact-info">
                    <strong>CONTACT INFORMATION:</strong> For any technical support and collaboration, <NAME_EMAIL>.
                </p>
                <p>Report for File Name: {analysis_data.get('filename', 'N/A')}</p>
            </footer>
        </div>
    </body>
    </html>"""

    # 生成PDF
    result = BytesIO()
    pdf = pisa.pisaDocument(BytesIO(html_template.encode("UTF-8")), result)

    if not pdf.err:
        return result.getvalue()
    else:
        raise Exception(f"PDF generation failed: {pdf.err}")

def download_report_pdf(request):
    """使用xhtml2pdf生成带有单词上色的PDF报告"""
    if request.method == 'POST':
        try:
            # 获取分析数据
            raw_data = request.POST.get('analysis_data', '{}')
            analysis_data = json.loads(raw_data)
            analysis_result = json.loads(analysis_data.get('result', '{}'))

            # 使用新的xhtml2pdf方法生成PDF
            pdf_bytes = generate_pdf_with_xhtml2pdf(analysis_data, analysis_result)

            # 创建HTTP响应
            response = HttpResponse(pdf_bytes, content_type='application/pdf')
            filename = f"cognitive-health-report-{analysis_data.get('filename', 'report').replace('.wav', '')}.pdf"
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except json.JSONDecodeError as e:
            return HttpResponse(f"Error decoding JSON: {e}", status=400)
        except Exception as e:
            return HttpResponse(f"An unexpected error occurred: {e}", status=500)

    return HttpResponse("Invalid request method.", status=405)

def upload_page(request):
    return render(request, 'upload.html', {'API_BASE_URL': API_BASE_URL})

def history_page(request):
    return render(request, 'history.html', {'API_BASE_URL': API_BASE_URL})

def details_page(request):
    return render(request, 'audio_details.html', {'API_BASE_URL': API_BASE_URL})

def profile_test(request):
    """用户Profile测试页面"""
    return render(request, 'html/profile_test.html', {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL
    })

def debug_profile(request):
    """Profile调试页面"""
    return render(request, 'html/debug_profile.html', {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL
    })

def profile_dropdown_test(request):
    """Profile下拉菜单测试页面"""
    return render(request, 'html/profile_dropdown_test.html', {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL
    })

def test_audio_history_api(request):
    """音频历史API测试页面"""
    return render(request, 'html/test_audio_history_api.html', {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL
    })